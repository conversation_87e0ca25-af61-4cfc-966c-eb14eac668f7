import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dropdown_button2/dropdown_button2.dart';

import '../../models/product.dart';
import '../../models/sale.dart';
import '../../models/category.dart' as model_category;
import '../../models/set_discount.dart';
import '../../models/set_discount_transaction.dart';
import '../../providers/settings_provider.dart';
import '../../models/sales_log.dart';
import '../../models/transaction_type.dart';
import '../../providers/product_provider.dart';
import '../../providers/category_provider.dart';
import '../../providers/sales_log_provider.dart';

import '../../providers/unified_workspace_provider.dart';
import '../../providers/seller_provider.dart';

import '../../models/product_sort_option.dart';

import '../../utils/currency_utils.dart';
import '../../utils/toast_utils.dart';
import '../../utils/subscription_utils.dart';

import '../../utils/image_sync_utils.dart';
import '../../widgets/sale_confirmation_dialog.dart';
import '../../widgets/unsaved_changes_dialog.dart';
import '../../widgets/change_calculator_dialog_new.dart';
import '../../widgets/tutorial_dialog.dart';


import '../../utils/product_display_utils.dart';
import '../../utils/dimens.dart';
import '../../utils/logger_utils.dart';
import '../../utils/dialog_theme.dart' as custom_dialog;
import '../../utils/app_colors.dart';
import '../../utils/device_utils.dart';
import '../../providers/set_discount_provider.dart';
import '../records_and_statistics/statistics_tab_content.dart';

import '../../repositories/set_discount_transaction_repository.dart';
import '../../services/set_discount_service.dart';
import '../product/manual_bulk_product_registration_screen.dart';
import 'sale_ui_components.dart' as sale_ui;
import '../set_discount/set_discount_dialog.dart';
import '../settings/payment_methods_dialog.dart';
import 'order_summary_panel.dart';
import 'fixed_bottom_bar.dart';
import '../product/register_product_screen.dart';

/// **Old 프로젝트의 다중 판매 페이지와 동일한 UI와 기능을 구현한 POS 판매 화면**
class SaleScreen extends ConsumerStatefulWidget {
  final bool startInEditMode;

  const SaleScreen({super.key, this.startInEditMode = false});

  @override
  ConsumerState<SaleScreen> createState() => _SaleScreenState();
}

class _SaleScreenState extends ConsumerState<SaleScreen>
    with RestorationMixin {
  final Map<int, int> _saleQuantities = {}; // 할인별 적용 수량
  bool _isEditMode = false; // 편집 모드 상태
  bool _isOrderPanelVisible = false; // 주문 패널 표시 상태
  bool _isDeletionMode = false; // 삭제 모드 상태 추가
  final Set<int> _selectedProductsForDeletion = {}; // 삭제용 선택된 상품 ID들

  // 깜빡임 방지를 위한 ValueNotifier 추가
  final ValueNotifier<Map<int, int>> _productQuantitiesNotifier = ValueNotifier<Map<int, int>>({});
  final ValueNotifier<Map<int, int>> _serviceQuantitiesNotifier = ValueNotifier<Map<int, int>>({});
  final ValueNotifier<int> _totalAmountNotifier = ValueNotifier<int>(0);
  final ValueNotifier<String?> _setDiscountInfoNotifier = ValueNotifier<String?>(null);

  // 수동 할인 관련 ValueNotifier 추가
  final ValueNotifier<bool> _manualDiscountEnabledNotifier = ValueNotifier<bool>(false);
  final ValueNotifier<int> _manualDiscountAmountNotifier = ValueNotifier<int>(0);

  @override
  String? get restorationId => 'sale_screen';
  @override
  void restoreState(RestorationBucket? oldBucket, bool initialRestore) {}

  @override
  void initState() {
    super.initState();

    // 편집 모드 초기화
    _isEditMode = widget.startInEditMode;

    // 수동 할인 변경 시 총 금액 재계산
    _manualDiscountEnabledNotifier.addListener(_calculateTotal);
    _manualDiscountAmountNotifier.addListener(_calculateTotal);

    // 초기 데이터 로드
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(productNotifierProvider.notifier).loadProducts();
      ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();
      ref.read(categoryNotifierProvider.notifier).loadCategories();
      // 활성 세트 할인 로드
      ref.read(setDiscountNotifierProvider.notifier).loadActiveSetDiscounts();
    });
  }

  @override
  void dispose() {
    // 리스너 제거
    _manualDiscountEnabledNotifier.removeListener(_calculateTotal);
    _manualDiscountAmountNotifier.removeListener(_calculateTotal);

    _productQuantitiesNotifier.dispose();
    _serviceQuantitiesNotifier.dispose();
    _totalAmountNotifier.dispose();
    _setDiscountInfoNotifier.dispose();
    _manualDiscountEnabledNotifier.dispose();
    _manualDiscountAmountNotifier.dispose();
    super.dispose();
  }



  @override
  Widget build(BuildContext context) {
    // 상품 상태를 watch해서 실시간 업데이트 감지
    ref.watch(productNotifierProvider);

    return PopScope(
      canPop: !_isEditMode, // 편집 모드가 아닐 때만 정상적으로 뒤로가기 허용
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;

        if (_isEditMode) {
          // 편집 모드일 때는 편집 모드만 종료
          setState(() {
            _isEditMode = false;
            _productQuantitiesNotifier.value = {};
            _serviceQuantitiesNotifier.value = {};
            // 삭제 모드도 함께 종료
            _isDeletionMode = false;
            _selectedProductsForDeletion.clear();
          });
        }
        // 일반 모드일 때는 정상적으로 뒤로가기 허용 (canPop: true로 설정됨)
      },
      child: Scaffold(
        appBar: AppBar(
          title: Material(
            color: Colors.transparent,
            child: InkWell(
              onLongPress: () {
                TutorialDialog.show(
                  context: context,
                  title: 'POS 사용 방법',
                  youtubeVideoId: 'cniOH9x5cfg',
                  description: '''POS 페이지에서 여러가지 기능을 설정할 수 있습니다.

POS 페이지의 모든 설정은 우측 상단의 연필모양 편집 버튼을 눌러서 가능합니다.

상품 등록:
• 단일 등록하기
각 카테고리에 있는 상품 추가 버튼을 눌러 단일 상품을 추가할 수 있습니다.
• 대량 등록하기
우측 상단의 설정 버튼을 눌러 상품 대량등록을 선택해주세요.
대량 상품 등록 우측 상단 +버튼을 눌러, 등록할 상품의 개수를 추가할 수 있습니다.
우측 상단의 마법봉 버튼을 눌러 모든 상품에 한번에 가격, 수량, 카테고리, 판매자 데이터를 기입할 수 있습니다.
모든 기입이 끝나면 우측 상단 체크모양 버튼을 눌러 등록을 완료해주세요.
가격이 같은 카테고리별로 등록하면 효율적으로 빠르게 상품을 등록할 수 있습니다.

상품 삭제:
우측 하단의 빨간색 삭제 버튼을 눌러주세요.
삭제할 상품을 체크하고 다시 삭제 버튼을 누르면 상품 삭제가 가능합니다.

카테고리 관리:
바라부스 매니저의 POS는 카테고리별로 상품을 관리하여, 빠르게 원하는 상품을 찾을 수 있습니다.
우측 상단의 설정 버튼을 눌러, 카테고리 관리를 선택해주세요.
카테고리를 추가 및 수정하고, 원하는 색상으로 등록해주세요. 카테고리 순서를 변경하면 실제 POS 화면의 정렬 순서도 변경됩니다.

세트할인 관리:
플러스 플랜에서는 세트할인 기능을 제공합니다.
세트할인은, 상품이 특정한 조합이나 조건이 맞춰졌을 때 자동으로 정해진 액수만큼 할인이 적용되는 기능입니다.
우측 상단의 설정 버튼을 눌러, 세트할인 관리를 선택해주세요.
세트할인은 4가지 종류를 지원합니다.
• 상품 조합 할인
특정 상품의 조합이 선택되면 자동으로 할인됩니다.
• 최소 구매 금액
설정한 금액 이상 선택되면 자동으로 할인됩니다.
• 카테고리별 수량 할인
특정 카테고리에서 정해진 개수만큼 선택되면 자동으로 할인됩니다.
• 지정상품 중 수량 할인
정해진 상품들 중, 정해진 개수만큼 선택되면 자동으로 할인됩니다.
자동으로 적용되는 여러가지 세트 할인을 등록하여, 편하게 관리해보세요.

결제수단 관리:
현금, 계좌이체, 카드 중 원하는 결제수단을 설정할 수 있습니다.
우측 상단의 설정 버튼을 눌러, 결제수단 관리를 선택해주세요.
원하시는 결제수단을 체크하시면, POS화면 하단에 선택한 결제수단 버튼만 활성화됩니다.
이후 통계에서 결제수단 별 판매액을 확인할 수 있어 편리합니다.

상품 크기 조정:
각 상품의 크기를 조절해서, 기기에 맞게 취향에 따라 상품 크기를 조절할 수 있습니다.
우측 상단의 설정 버튼을 눌러, 상품 크기 조정을 눌러주세요.
한 열에 몇개의 상품이 보일지 조정해 주세요. 숫자가 크면 상품 크기가 작아지고, 숫자가 작으면 상품 크기가 커집니다.

이미지 표시 설정:
만약 상품 이미지를 등록하기 귀찮거나, 마땅한 이미지가 없거나, 더 간결한 UI를 원할 경우 이미지 옵션을 끌 수 있습니다.
우측 상단의 설정 버튼을 눌러, 이미지 OFF를 눌러주세요.
이미지 표시 부분이 없어지고 상품별 세로 길이가 짧아져 한번에 더 많은 상품을 볼 수 있습니다.
취향에 따라 POS 페이지를 구성해보세요.

상품을 터치하여 장바구니에 추가하고, 우측 하단의 주문 내역 버튼으로 상세 정보를 확인할 수 있습니다.
편집 모드에서는 상품 등록, 카테고리 관리, 세트할인 설정 등의 관리 기능을 사용할 수 있습니다.

상품 판매:
우측 하단의 버튼을 눌러 주문내역 패널을 열고 닫을 수 있습니다.
상품을 눌러, 판매할 상품을 추가합니다.
주문 패널의 상품 개수 부분을 슬라이드해 수량을 조절하거나, 터치해 직접 입력할 수 있습니다.
원하는 결제 방법의 버튼을 누르면, 판매가 진행됩니다.

선택 상품 해제:
상품을 왼쪽으로 슬라이드 해 상품 삭제 버튼을 눌러 삭제할 수 있습니다.
수량 조절 슬라이드를 0으로 만들어 상품선택을 해제할 수 있습니다.
페이지 상단 리셋 버튼을 눌러, 모든 등록을 해제할 수 있습니다.

할인하기:
주문 패널의 우측 '할인하기'버튼을 눌러 직접 할인금액을 설정해 할인할 수 있습니다.

거스름돈 계산:
현금 버튼을 눌러 판매할 경우, 판매 확인창에서 거스름돈 계산하기를 선택할 수 있습니다.
빠르게 거스름돈을 계산할 수 있어 편리합니다.''',
                );
              },
              borderRadius: BorderRadius.circular(8),
              splashColor: Colors.white.withValues(alpha: 0.2),
              highlightColor: Colors.white.withValues(alpha: 0.1),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                child: Text(
                  'POS',
                  style: Theme.of(context).textTheme.titleLarge!.copyWith(
                    fontFamily: 'Pretendard',
                    color: Theme.of(context).colorScheme.onPrimary,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
          centerTitle: true,
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Theme.of(context).colorScheme.onPrimary,
          actions: [
            // 편집 모드일 때 톱니바퀴 메뉴 표시
            if (_isEditMode)
              Consumer(
                builder: (context, ref, child) {
                  final showImages = ref.watch(showProductImagesProvider);

                  return PopupMenuButton<String>(
                    icon: const Icon(Icons.settings),
                    tooltip: '관리 메뉴',
                    position: PopupMenuPosition.under,
                    offset: const Offset(0, 4),
                    onSelected: (value) {
                      switch (value) {
                        case 'category':
                          _showCategoryManagementDialog();
                          break;
                        case 'set_discount':
                          _showSetDiscountManagementDialog();
                          break;
                        case 'ui_columns':
                          _showUIColumnsSettings();
                          break;
                        case 'toggle_images':
                          ref.read(settingsNotifierProvider.notifier).setShowProductImages(!showImages);
                          break;
                        case 'payment_methods':
                          _showPaymentMethodsDialog();
                          break;
                        case 'bulk_registration':
                          _showBulkRegistrationScreen();
                          break;
                      }
                    },
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'bulk_registration',
                    child: Row(
                      children: [
                        Icon(Icons.add_box, size: 18, color: Theme.of(context).colorScheme.primary),
                        const SizedBox(width: 8),
                        const Text('상품 대량등록'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'category',
                    child: Row(
                      children: [
                        Icon(Icons.folder, size: 18, color: Theme.of(context).colorScheme.primary),
                        const SizedBox(width: 8),
                        const Text('카테고리 관리'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'set_discount',
                    child: Row(
                      children: [
                        Icon(Icons.local_offer, size: 18, color: Theme.of(context).colorScheme.primary),
                        const SizedBox(width: 8),
                        const Text('세트할인 관리'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'payment_methods',
                    child: Row(
                      children: [
                        Icon(Icons.payment, size: 18, color: Theme.of(context).colorScheme.primary),
                        const SizedBox(width: 8),
                        const Text('결제수단 관리'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'ui_columns',
                    child: Row(
                      children: [
                        Icon(Icons.grid_view, size: 18, color: Theme.of(context).colorScheme.primary),
                        const SizedBox(width: 8),
                        const Text('상품 크기 조정'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'toggle_images',
                    child: Row(
                      children: [
                        Icon(
                          showImages ? Icons.image_not_supported : Icons.image,
                          size: 18,
                          color: showImages ? Colors.orange : Colors.green,
                        ),
                        const SizedBox(width: 8),
                        Text(showImages ? '이미지 OFF' : '이미지 ON'),
                      ],
                    ),
                  ),
                    ],
                  );
                },
              ),
            // 주문 내역 초기화 버튼
            if (!_isEditMode)
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: _refreshOrderData,
                tooltip: '주문 내역 초기화',
              ),
            // 편집 모드 토글 버튼
            IconButton(
              icon: Icon(_isEditMode ? Icons.check : Icons.edit),
              onPressed: () {
                setState(() {
                  _isEditMode = !_isEditMode;
                  if (_isEditMode) {
                    // 편집 모드 진입 시 선택된 상품들과 서비스 수량 초기화
                    _productQuantitiesNotifier.value = {};
                    _serviceQuantitiesNotifier.value = {};
                    // 수동 할인도 초기화
                    _manualDiscountEnabledNotifier.value = false;
                    _manualDiscountAmountNotifier.value = 0;
                    // 총 금액 재계산하여 UI 즉시 업데이트
                    _calculateTotal();
                  } else {
                    // 편집 모드 종료 시 삭제 모드도 함께 종료
                    _isDeletionMode = false;
                    _selectedProductsForDeletion.clear();
                  }
                });
              },
              tooltip: _isEditMode ? '편집 완료' : '편집 모드',
            ),
            // 정렬 및 필터링 버튼
            IconButton(
              icon: const Icon(Icons.sort),
              onPressed: _showSortAndFilterDialog,
              tooltip: '정렬 및 필터',
            ),
          ],
        ),
        body: SafeArea(
          child: _buildResponsiveLayout(),
        ),
      ),
    );
  }

  /// 반응형 레이아웃 빌더
  Widget _buildResponsiveLayout() {
    final orientation = MediaQuery.of(context).orientation;
    final isLandscape = orientation == Orientation.landscape;

    return Stack(
      children: [
        // 메인 콘텐츠 영역
        Column(
          children: [
            Expanded(
              child: isLandscape ? _buildLandscapeLayout() : _buildPortraitLayout(),
            ),
            // 고정 하단 바
            FixedBottomBar(
              productQuantitiesNotifier: _productQuantitiesNotifier,
              serviceQuantitiesNotifier: _serviceQuantitiesNotifier,
              totalAmountNotifier: _totalAmountNotifier,
              setDiscountInfoNotifier: _setDiscountInfoNotifier,
              isOrderListVisible: _isOrderPanelVisible,
              onToggleOrderList: () {
                setState(() {
                  _isOrderPanelVisible = !_isOrderPanelVisible;
                });
              },
              onSellWithMethod: _onSellWithMethod,
            ),
          ],
        ),

        // 토글 버튼 - 오른쪽 하단 (판매 바 바로 위)
        if (!_isEditMode)
          _buildToggleButton(),

        // 편집 모드일 때 삭제 관련 버튼들
        if (_isEditMode)
          _buildDeletionModeButtons(),
      ],
    );
  }







  /// 상품 그리드 영역
  Widget _buildProductGridArea() {
    return Padding(
      padding: const EdgeInsets.all(4),
      child: RepaintBoundary(
        child: _ProductGrid(
          productQuantitiesNotifier: _productQuantitiesNotifier,
          onProductTap: _isDeletionMode
            ? (Product product) {
                if (product.id != null) {
                  _toggleProductForDeletion(product.id!);
                }
              }
            : (_isEditMode ? _onProductEditTap : _onProductTap),
          onProductLongPress: _isDeletionMode ? null : (_isEditMode ? null : _onProductLongPress),
          onProductDecrease: _isDeletionMode ? null : (_isEditMode ? null : _onProductDecrease),
          onSaleTap: _isDeletionMode ? null : (_isEditMode ? null : _onSaleTap),
          onSaleLongPress: _isDeletionMode ? null : (_isEditMode ? null : _onSaleLongPress),
          isEditMode: _isEditMode,
          isOrderPanelVisible: _isOrderPanelVisible,
          isDeletionMode: _isDeletionMode,
          selectedProductsForDeletion: _selectedProductsForDeletion,
          onProductCheckToggle: _isDeletionMode ? _toggleProductForDeletion : null,
        ),
      ),
    );
  }



  /// 가로 모드 레이아웃 (좌측: 상품 그리드, 우측: 주문 패널)
  Widget _buildLandscapeLayout() {
    return Stack(
      children: [
        // 상품 그리드 영역 - 주문 패널 상태에 따라 너비 조정
        Positioned(
          left: 0,
          top: 0,
          bottom: 0,
          right: (_isOrderPanelVisible && !_isEditMode) ? 388 : 0, // 패널 너비 + 여백
          child: _buildProductGridArea(),
        ),

        // 주문 패널 영역 - 오른쪽에서 슬라이드 애니메이션
        AnimatedPositioned(
          duration: const Duration(milliseconds: 400),
          curve: Curves.easeOutCubic,
          right: (_isOrderPanelVisible && !_isEditMode) ? 0 : -380,
          top: 0,
          bottom: 0,
          child: Container(
            width: 380,
            child: _buildOrderPanel(),
          ),
        ),
      ],
    );
  }

  /// 세로 모드 레이아웃 (상단: 상품 그리드, 하단: 주문 패널)
  Widget _buildPortraitLayout() {
    return Column(
      children: [
        // 상품 그리드 영역 - 주문 패널 상태에 따라 높이 조정
        Expanded(
          child: _buildProductGridArea(),
        ),

        // 주문 패널 영역 - 아래에서 슬라이드 애니메이션 (동적 높이)
        AnimatedContainer(
          duration: const Duration(milliseconds: 400),
          curve: Curves.easeOutCubic,
          height: (_isOrderPanelVisible && !_isEditMode) ? 400 : 0,
          child: ClipRect(
            child: AnimatedSlide(
              duration: const Duration(milliseconds: 400),
              curve: Curves.easeOutCubic,
              offset: (_isOrderPanelVisible && !_isEditMode) ? Offset.zero : const Offset(0, 1),
              child: Container(
                height: 400,
                child: _buildOrderPanel(),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 주문 패널
  Widget _buildOrderPanel() {
    return OrderSummaryPanel(
      productQuantitiesNotifier: _productQuantitiesNotifier,
      serviceQuantitiesNotifier: _serviceQuantitiesNotifier,
      onProductIncrease: _onProductTap,
      onProductDecrease: _onProductDecrease,
      onServiceIncrease: _onServiceTap,
      onServiceDecrease: _onServiceDecrease,
      onProductRemove: _onProductRemove,
      onServiceRemove: _onServiceRemove,
      manualDiscountEnabledNotifier: _manualDiscountEnabledNotifier,
      manualDiscountAmountNotifier: _manualDiscountAmountNotifier,
      setDiscountInfoNotifier: _setDiscountInfoNotifier,
    );
  }

  /// 토글 버튼 (오른쪽 하단, 판매 바 바로 위)
  Widget _buildToggleButton() {
    return Positioned(
      right: 16,
      bottom: 88, // 고정 하단 바 높이 + 여백
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: AppColors.primarySeed.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: AppColors.primarySeed.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: IconButton(
          onPressed: () {
            setState(() {
              _isOrderPanelVisible = !_isOrderPanelVisible;
            });
          },
          icon: Icon(
            _isOrderPanelVisible ? Icons.receipt_long : Icons.receipt,
            color: Colors.white,
            size: 20,
          ),
          tooltip: _isOrderPanelVisible ? '주문 내역 닫기' : '주문 내역 열기',
        ),
      ),
    );
  }

  /// 편집 모드에서 삭제 관련 버튼들 (우측 하단)
  Widget _buildDeletionModeButtons() {
    return Positioned(
      right: 16,
      bottom: 88, // 고정 하단 바 높이 + 여백
      child: _isDeletionMode
        ? Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 취소 버튼
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: Colors.grey.shade600,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: IconButton(
                  onPressed: _toggleDeletionMode,
                  icon: const Icon(Icons.close, color: Colors.white, size: 20),
                  tooltip: '삭제 모드 취소',
                ),
              ),
              const SizedBox(width: 8),
              // 확인 버튼
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: AppColors.categoryRed,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.categoryRed.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: IconButton(
                  onPressed: _deleteSelectedProducts,
                  icon: const Icon(Icons.delete, color: Colors.white, size: 20),
                  tooltip: '선택된 상품 삭제',
                ),
              ),
            ],
          )
        : Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: AppColors.categoryRed.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: AppColors.categoryRed.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: IconButton(
              onPressed: _toggleDeletionMode,
              icon: const Icon(Icons.delete_outline, color: Colors.white, size: 20),
              tooltip: '삭제 모드',
            ),
          ),
    );
  }

  // 상품 탭 처리
  void _onProductTap(Product product) {
    LoggerUtils.logDebug('상품 탭됨: ${product.name} (ID: ${product.id})', tag: 'SaleScreen');
    if (product.id == null) return; // id가 null이면 처리하지 않음

    final currentQuantities = Map<int, int>.from(_productQuantitiesNotifier.value);
    final currentQuantity = currentQuantities[product.id!] ?? 0;
    LoggerUtils.logDebug('현재 수량: $currentQuantity, 재고: ${product.quantity}', tag: 'SaleScreen');

    if (currentQuantity < product.quantity) {
      currentQuantities[product.id!] = currentQuantity + 1;
      _productQuantitiesNotifier.value = currentQuantities;
      LoggerUtils.logDebug('수량 증가: ${currentQuantities[product.id!]}', tag: 'SaleScreen');
      _calculateTotal();
    } else {
      LoggerUtils.logDebug('재고 부족으로 수량 증가 불가', tag: 'SaleScreen');
    }
  }

  // 상품 롱프레스 처리 (서비스 증정)
  void _onProductLongPress(Product product) async {
    // 서비스 기능 사용 권한 확인
    final hasAccess = await SubscriptionUtils.checkFeatureAccess(
      ref: ref,
      context: context,
      featureName: 'service',
    );

    if (!hasAccess) {
      return;
    }

    // 재고가 있는 경우에만 서비스 증정 가능
    if (product.quantity > 0) {
      // 바로 서비스 1개 추가
      final currentServiceQuantities = Map<int, int>.from(_serviceQuantitiesNotifier.value);
      final currentQuantity = currentServiceQuantities[product.id!] ?? 0;
      currentServiceQuantities[product.id!] = currentQuantity + 1;
      _serviceQuantitiesNotifier.value = currentServiceQuantities;
      _calculateTotal();
    } else {
      ToastUtils.showError(context, '재고가 없어 서비스 증정할 수 없습니다.');
    }
  }

  // 상품 수량 감소 처리
  void _onProductDecrease(Product product) {
    if (product.id == null) return; // id가 null이면 처리하지 않음

    final currentQuantities = Map<int, int>.from(_productQuantitiesNotifier.value);
    final currentQuantity = currentQuantities[product.id!] ?? 0;

    if (currentQuantity > 1) {
      currentQuantities[product.id!] = currentQuantity - 1;
      LoggerUtils.logDebug('수량 감소: ${currentQuantities[product.id!]}', tag: 'SaleScreen');
    } else {
      currentQuantities.remove(product.id!);
      LoggerUtils.logDebug('상품 제거: ${product.name}', tag: 'SaleScreen');
    }

    _productQuantitiesNotifier.value = currentQuantities;
    _calculateTotal();
  }

  // 편집 모드에서 상품 탭 처리 (재고관리 다이얼로그 표시)
  void _onProductEditTap(Product product) {
    _showProductActionDialog(context, product, ref);
  }

  // 서비스 탭 처리 (증가)
  void _onServiceTap(Product product) async {
    if (product.id == null) return; // id가 null이면 처리하지 않음

    // 서비스 기능 사용 권한 확인
    final hasAccess = await SubscriptionUtils.checkFeatureAccess(
      ref: ref,
      context: context,
      featureName: 'service',
    );

    if (!hasAccess) {
      return;
    }

    final currentQuantities = Map<int, int>.from(_serviceQuantitiesNotifier.value);
    final currentQuantity = currentQuantities[product.id!] ?? 0;

    // 서비스는 재고 제한 없이 추가 가능
    currentQuantities[product.id!] = currentQuantity + 1;
    _serviceQuantitiesNotifier.value = currentQuantities;
    _calculateTotal();

    LoggerUtils.logDebug('서비스 수량 증가: ${product.name} x${currentQuantities[product.id!]}', tag: 'SaleScreen');
  }

  // 서비스 수량 감소 처리
  void _onServiceDecrease(Product product) {
    if (product.id == null) return; // id가 null이면 처리하지 않음

    final currentQuantities = Map<int, int>.from(_serviceQuantitiesNotifier.value);
    final currentQuantity = currentQuantities[product.id!] ?? 0;

    if (currentQuantity > 1) {
      currentQuantities[product.id!] = currentQuantity - 1;
      LoggerUtils.logDebug('서비스 수량 감소: ${currentQuantities[product.id!]}', tag: 'SaleScreen');
    } else {
      currentQuantities.remove(product.id!);
      LoggerUtils.logDebug('서비스 제거: ${product.name}', tag: 'SaleScreen');
    }

    _serviceQuantitiesNotifier.value = currentQuantities;
    _calculateTotal();
  }

  // 상품 완전 삭제 처리 (슬라이드 삭제용)
  void _onProductRemove(Product product) {
    if (product.id == null) return; // id가 null이면 처리하지 않음

    final currentQuantities = Map<int, int>.from(_productQuantitiesNotifier.value);
    currentQuantities.remove(product.id!);
    LoggerUtils.logDebug('상품 완전 삭제: ${product.name}', tag: 'SaleScreen');

    _productQuantitiesNotifier.value = currentQuantities;
    _calculateTotal();
  }

  // 서비스 완전 삭제 처리 (슬라이드 삭제용)
  void _onServiceRemove(Product product) {
    if (product.id == null) return; // id가 null이면 처리하지 않음

    final currentQuantities = Map<int, int>.from(_serviceQuantitiesNotifier.value);
    currentQuantities.remove(product.id!);
    LoggerUtils.logDebug('서비스 완전 삭제: ${product.name}', tag: 'SaleScreen');

    _serviceQuantitiesNotifier.value = currentQuantities;
    _calculateTotal();
  }

  /// 상품 액션 다이얼로그 (재고관리와 동일한 다이얼로그)
  void _showProductActionDialog(BuildContext context, Product product, WidgetRef ref) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    showDialog(
      context: context,
      builder: (context) => custom_dialog.DialogTheme.buildModernDialog(
        isCompact: true,
        child: Stack(
          children: [
            Padding(
              padding: custom_dialog.DialogTheme.getCompactDialogPadding(isTablet),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 아이콘과 제목을 한 줄로 배치
                  Row(
                    children: [
                      custom_dialog.DialogTheme.buildCompactIconContainer(
                        icon: Icons.inventory_2_rounded,
                        color: AppColors.onboardingPrimary,
                        isTablet: isTablet,
                      ),
                      SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet) * 1.5),
                      Expanded(
                        child: Consumer(
                          builder: (context, ref, child) {
                            final categories = ref.watch(categoryNotifierProvider).value ?? [];
                            final displayName = ProductDisplayUtils.getDisplayNameWithCategory(product, categories);
                            return Text(
                              displayName,
                              style: custom_dialog.DialogTheme.titleStyle.copyWith(
                                fontSize: isTablet ? 20.0 : 18.0,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            );
                          },
                        ),
                      ),
                    ],
                  ),
              SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),

              // 상품 정보 (더 컴팩트한 그리드 레이아웃)
              Container(
                padding: custom_dialog.DialogTheme.getCompactPadding(isTablet),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppColors.surfaceVariant,
                      AppColors.secondary.withValues(alpha: 0.3),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    // 첫 번째 행: 판매자, 가격
                    Row(
                      children: [
                        Expanded(child: _buildCompactInfoItem('판매자', product.sellerName ?? '미지정', isTablet)),
                        SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet)),
                        Expanded(child: _buildCompactInfoItem('가격', '${CurrencyUtils.formatCurrency(product.price)}원', isTablet)),
                      ],
                    ),
                    SizedBox(height: custom_dialog.DialogTheme.getCompactSpacing(isTablet)),
                    // 두 번째 행: 재고
                    Row(
                      children: [
                        Expanded(child: _buildCompactInfoItem('재고', '${product.quantity}개', isTablet)),
                        SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet)),
                        // 빈 공간
                        Expanded(child: Container()),
                      ],
                    ),
                  ],
                ),
              ),
              SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet) * 1.5),

              // 버튼들 (더 컴팩트한 레이아웃)
              Row(
                children: [
                  // 닫기 버튼
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.secondary),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Material(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.circular(12),
                        child: InkWell(
                          borderRadius: BorderRadius.circular(12),
                          onTap: () => _showCopyProductDialog(product),
                          child: Container(
                            padding: EdgeInsets.symmetric(
                              vertical: isTablet ? 12.0 : 10.0,
                            ),
                            child: Text(
                              '복사',
                              style: TextStyle(
                                color: AppColors.onboardingTextSecondary,
                                fontSize: isTablet ? 14.0 : 13.0,
                                fontWeight: FontWeight.w600,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet)),

                  // 수정 버튼
                  Expanded(
                    child: custom_dialog.DialogTheme.buildGradientButton(
                      decoration: custom_dialog.DialogTheme.confirmButtonDecoration,
                      isCompact: true,
                      onPressed: () async {
                        if (context.mounted) {
                          Navigator.of(context).pop();
                        }
                        if (context.mounted) {
                          final result = await Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => RegisterProductScreen(product: product, isEditing: true),
                            ),
                          );
                          if (result == true && context.mounted) {
                            await ref.read(productNotifierProvider.notifier).loadProducts();
                          }
                        }
                      },
                      child: Text(
                        '수정',
                        style: TextStyle(
                          color: AppColors.onboardingTextOnPrimary,
                          fontSize: isTablet ? 14.0 : 13.0,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        // 우측 상단 X 버튼
        Positioned(
          top: 8,
          right: 8,
          child: GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              width: isTablet ? 32 : 28,
              height: isTablet ? 32 : 28,
              decoration: BoxDecoration(
                color: Colors.grey.shade600,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Icon(
                Icons.close,
                color: Colors.white,
                size: isTablet ? 18 : 16,
              ),
            ),
          ),
        ),
      ],
    ),
      ),
    );
  }



  /// 삭제용 상품 체크 토글
  void _toggleProductForDeletion(int productId) {
    setState(() {
      if (_selectedProductsForDeletion.contains(productId)) {
        _selectedProductsForDeletion.remove(productId);
      } else {
        _selectedProductsForDeletion.add(productId);
      }
    });
  }

  /// 삭제 모드 토글
  void _toggleDeletionMode() {
    setState(() {
      _isDeletionMode = !_isDeletionMode;
      if (!_isDeletionMode) {
        _selectedProductsForDeletion.clear(); // 삭제 모드 종료 시 선택 초기화
      }
    });
  }

  /// 선택된 상품들 일괄 삭제
  void _deleteSelectedProducts() async {
    if (_selectedProductsForDeletion.isEmpty) {
      ToastUtils.showWarning(context, '삭제할 상품을 선택해주세요.');
      return;
    }

    final products = ref.watch(productNotifierProvider).products;
    final productsToDelete = products.where((p) => _selectedProductsForDeletion.contains(p.id)).toList();

    if (productsToDelete.isEmpty) {
      ToastUtils.showWarning(context, '선택된 상품을 찾을 수 없습니다.');
      return;
    }

    // 상품 이름들을 표시하여 사용자가 확인할 수 있도록 함
    final productNames = productsToDelete.map((p) => p.name).take(3).join(', ');
    final displayText = productsToDelete.length > 3
      ? '$productNames 외 ${productsToDelete.length - 3}개'
      : productNames;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('상품 삭제 확인'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('다음 ${productsToDelete.length}개 상품을 삭제하시겠습니까?'),
            const SizedBox(height: 8),
            Text(
              displayText,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: AppColors.categoryRed,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '삭제된 상품은 복구할 수 없습니다.',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.categoryRed),
            child: const Text('삭제', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // 로딩 표시
        if (mounted) {
          ToastUtils.showInfo(context, '${productsToDelete.length}개 상품을 삭제하는 중...');
        }

        // 배치 삭제로 서버 사용량 최적화 및 속도 향상
        final results = await ref.read(productNotifierProvider.notifier).deleteBatchProducts(productsToDelete);
        final successCount = results['success'] as int;
        final failCount = results['failed'] as int;

        setState(() {
          _isDeletionMode = false;
          _selectedProductsForDeletion.clear();
        });

        if (mounted) {
          if (failCount == 0) {
            ToastUtils.showSuccess(context, '${successCount}개 상품이 삭제되었습니다.');
          } else {
            ToastUtils.showWarning(context, '${successCount}개 삭제 완료, ${failCount}개 실패');
          }
        }
      } catch (e) {
        if (mounted) {
          ToastUtils.showError(context, '상품 삭제 중 오류가 발생했습니다: $e');
        }
      }
    }
  }

  /// 상품 복사 다이얼로그 표시
  void _showCopyProductDialog(Product product) async {
    Navigator.of(context).pop(); // 현재 다이얼로그 닫기

    // 상품 등록 화면으로 이동 (복사 모드)
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RegisterProductScreen(
          product: product,
          isEditing: false, // 새 상품 생성 모드
          isCopyMode: true, // 복사 모드 플래그 추가 필요
        ),
      ),
    );

    if (result == true && mounted) {
      await ref.read(productNotifierProvider.notifier).loadProducts();
    }
  }

  /// 컴팩트한 정보 아이템 빌더
  Widget _buildCompactInfoItem(String label, String value, bool isTablet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isTablet ? 12.0 : 11.0,
            color: AppColors.onboardingTextSecondary,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 2),
        Text(
          value,
          style: TextStyle(
            fontSize: isTablet ? 14.0 : 13.0,
            color: AppColors.onboardingTextPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  // 할인 탭 처리
  void _onSaleTap(Sale sale) {
    setState(() {
      final currentQuantity = _saleQuantities[sale.id] ?? 0;
      _saleQuantities[sale.id!] = currentQuantity + 1;
      _calculateTotal();
    });
  }

  // 할인 롱프레스 처리
  void _onSaleLongPress(Sale sale) {
    setState(() {
      final currentQuantity = _saleQuantities[sale.id] ?? 0;
      if (currentQuantity > 1) {
        _saleQuantities[sale.id!] = currentQuantity - 1;
      } else {
        _saleQuantities.remove(sale.id);
      }
      _calculateTotal();
    });
  }

  // 총 금액 계산 (세트 할인 적용)
  Future<void> _calculateTotal() async {
    final allProducts = ref
        .watch(productNotifierProvider)
        .products
        .where((product) => product.isActive)
        .toList();

    int total = 0;
    final quantities = _productQuantitiesNotifier.value;

    LoggerUtils.logDebug('총 금액 계산 시작', tag: 'SaleScreen');
    LoggerUtils.logDebug('선택된 상품 수: ${quantities.length}', tag: 'SaleScreen');
    LoggerUtils.logDebug('전체 활성 상품 수: ${allProducts.length}', tag: 'SaleScreen');

    // 상품 금액 합계
    quantities.forEach((productId, quantity) {
      try {
        final product = allProducts.firstWhere((p) => p.id == productId);
        final itemTotal = (product.price * quantity).round();
        total += itemTotal;
        LoggerUtils.logDebug('상품: ${product.name}, 수량: $quantity, 가격: ${product.price}, 소계: $itemTotal', tag: 'SaleScreen');
      } catch (e) {
        LoggerUtils.logDebug('상품을 찾을 수 없음: ID $productId', tag: 'SaleScreen');
        // 상품을 찾을 수 없는 경우 무시
      }
    });

    // 할인 적용 (플랜 제한 확인)
    int finalTotal = total > 0 ? total : 0;
    String? setDiscountInfo;
    int setDiscountAmount = 0;

    try {
      // 수동 할인 금액 계산
      final manualDiscountAmount = _manualDiscountAmountNotifier.value;
      int totalAfterManualDiscount = finalTotal;
      if (_manualDiscountEnabledNotifier.value && manualDiscountAmount > 0) {
        totalAfterManualDiscount = finalTotal - manualDiscountAmount;
        if (totalAfterManualDiscount < 0) totalAfterManualDiscount = 0;
      }

      // 세트 할인 기능 사용 가능 여부 확인
      final hasSetDiscountFeature = await SubscriptionUtils.hasFeature(ref, 'setDiscount');

      if (hasSetDiscountFeature) {
        final activeSetDiscounts = ref.read(activeSetDiscountsProvider);
        final setDiscountResult = SetDiscountService.calculateOptimalDiscount(
          quantities,
          activeSetDiscounts,
          allProducts: allProducts,
          totalAmount: total, // 할인 적용 전 금액
          totalAmountAfterOtherDiscounts: totalAfterManualDiscount, // 다른 할인 적용 후 금액
        );

        setDiscountAmount = setDiscountResult.totalDiscountAmount;

        // 세트 할인 정보 생성
        if (setDiscountAmount > 0) {
          setDiscountInfo = '세트 할인: ${setDiscountAmount}원';
        }
      }

      // 최종 금액 계산 (세트 할인 + 수동 할인)
      finalTotal = total - setDiscountAmount;
      if (_manualDiscountEnabledNotifier.value && manualDiscountAmount > 0) {
        finalTotal -= manualDiscountAmount;
      }

      if (finalTotal < 0) finalTotal = 0;

      LoggerUtils.logDebug('원래 금액: ${CurrencyUtils.formatCurrency(total)}', tag: 'SaleScreen');
      LoggerUtils.logDebug('세트 할인: ${CurrencyUtils.formatCurrency(hasSetDiscountFeature ? (setDiscountInfo != null ? finalTotal - total : 0) : 0)}', tag: 'SaleScreen');
      LoggerUtils.logDebug('수동 할인: ${CurrencyUtils.formatCurrency(manualDiscountAmount)}', tag: 'SaleScreen');
      LoggerUtils.logDebug('최종 금액: ${CurrencyUtils.formatCurrency(finalTotal)}', tag: 'SaleScreen');
    } catch (e) {
      LoggerUtils.logError('세트 할인 계산 실패', error: e, tag: 'SaleScreen');
    }

    _totalAmountNotifier.value = finalTotal;
    _setDiscountInfoNotifier.value = setDiscountInfo;
    LoggerUtils.logDebug('총 금액: $finalTotal', tag: 'SaleScreen');
  }

  // 판매 확인 다이얼로그 (세트 할인 정보 포함)
  Future<void> _onSellWithMethod(String method) async {
    LoggerUtils.logDebug('판매 확인 다이얼로그 시작', tag: 'SaleScreen');
    final totalAmount = _totalAmountNotifier.value;
    final quantities = _productQuantitiesNotifier.value;
    final serviceQuantities = _serviceQuantitiesNotifier.value;
    LoggerUtils.logDebug('총 금액: $totalAmount', tag: 'SaleScreen');
    LoggerUtils.logDebug('선택된 상품 수: ${quantities.length}', tag: 'SaleScreen');
    LoggerUtils.logDebug('서비스 상품 수: ${serviceQuantities.length}', tag: 'SaleScreen');

    final allProducts = ref
        .watch(productNotifierProvider)
        .products
        .where((product) => product.isActive)
        .toList();

    // 카테고리 정보 가져오기
    final categoriesAsync = ref.read(categoryNotifierProvider);
    final categories = categoriesAsync.hasValue ? categoriesAsync.value! : <model_category.Category>[];

    // SaleItem 리스트 생성
    final saleItems = quantities.entries
        .where((entry) => entry.value > 0)
        .map((entry) {
          try {
            final product = allProducts.firstWhere((p) => p.id == entry.key);
            // 카테고리명과 상품명 조합
            String displayName = product.name;
            try {
              final category = categories.firstWhere(
                (cat) => cat.id == product.categoryId,
              );
              displayName = '[${category.name}] ${product.name}';
            } catch (e) {
              // 카테고리를 찾을 수 없는 경우 원본 상품명 사용
            }

            return SaleItem(
              displayName: displayName,
              quantity: entry.value,
              unitPrice: product.price,
              subtotal: product.price * entry.value,
            );
          } catch (e) {
            return SaleItem(
              displayName: '알 수 없는 상품',
              quantity: entry.value,
              unitPrice: 0,
              subtotal: 0,
            );
          }
        })
        .toList();

    // 서비스 항목 생성
    final serviceItems = serviceQuantities.entries
        .where((entry) => entry.value > 0)
        .map((entry) {
          try {
            final product = allProducts.firstWhere((p) => p.id == entry.key);
            // 카테고리명과 상품명 조합
            String displayName = product.name;
            try {
              final category = categories.firstWhere(
                (cat) => cat.id == product.categoryId,
              );
              displayName = '[${category.name}] ${product.name}';
            } catch (e) {
              // 카테고리를 찾을 수 없는 경우 원본 상품명 사용
            }

            return SaleItem(
              displayName: '$displayName (서비스)',
              quantity: entry.value,
              unitPrice: 0, // 서비스는 가격이 0
              subtotal: 0,
            );
          } catch (e) {
            return SaleItem(
              displayName: '알 수 없는 상품 (서비스)',
              quantity: entry.value,
              unitPrice: 0,
              subtotal: 0,
            );
          }
        })
        .toList();

    LoggerUtils.logDebug('선택된 상품 수: ${saleItems.length}', tag: 'SaleScreen');
    LoggerUtils.logDebug('서비스 항목 수: ${serviceItems.length}', tag: 'SaleScreen');

    // 세트 할인 정보 계산 (플랜 제한 확인)
    String? setDiscountInfo;
    try {
      final hasSetDiscountFeature = await SubscriptionUtils.hasFeature(ref, 'setDiscount');

      if (hasSetDiscountFeature) {
        final activeSetDiscounts = ref.read(activeSetDiscountsProvider);
        final allProducts = ref
            .read(productNotifierProvider)
            .products
            .where((product) => product.isActive)
            .toList();

        // 수동 할인 적용 후 금액 계산
        final manualDiscountAmount = _manualDiscountAmountNotifier.value;
        int totalAfterManualDiscount = totalAmount;
        if (_manualDiscountEnabledNotifier.value && manualDiscountAmount > 0) {
          totalAfterManualDiscount = totalAmount - manualDiscountAmount;
          if (totalAfterManualDiscount < 0) totalAfterManualDiscount = 0;
        }

        final setDiscountResult = SetDiscountService.calculateOptimalDiscount(
          quantities,
          activeSetDiscounts,
          allProducts: allProducts,
          totalAmount: totalAmount, // 할인 적용 전 금액
          totalAmountAfterOtherDiscounts: totalAfterManualDiscount, // 다른 할인 적용 후 금액
        );

        if (setDiscountResult.totalDiscountAmount > 0) {
          setDiscountInfo = '세트 할인: ${setDiscountResult.totalDiscountAmount}원';
        }
      }
    } catch (e) {
      LoggerUtils.logError('세트 할인 정보 계산 실패', error: e, tag: 'SaleScreen');
    }

    // 판매 항목과 서비스 항목을 합침
    final allItems = [...saleItems, ...serviceItems];

    LoggerUtils.logDebug('다이얼로그 표시 시작', tag: 'SaleScreen');
    final result = await SaleConfirmationDialog.show(
      context: context,
      items: allItems,
      totalAmount: totalAmount,
      setDiscountInfo: setDiscountInfo,
      manualDiscountAmount: _manualDiscountEnabledNotifier.value ? _manualDiscountAmountNotifier.value : 0,
      paymentMethod: method,
      onConfirm: () {
        LoggerUtils.logDebug('판매 확인됨 - 판매 처리 시작', tag: 'SaleScreen');
      },
      onConfirmWithChange: () {
        LoggerUtils.logDebug('판매 확인됨 (잔돈 계산 포함) - 판매 처리 시작', tag: 'SaleScreen');
      },
      onCancel: () {
        LoggerUtils.logDebug('판매 취소됨', tag: 'SaleScreen');
      },
    );

    LoggerUtils.logDebug('다이얼로그 결과: $result', tag: 'SaleScreen');
    if (result == 'confirm') {
      // 일반 판매 확정
      _completeSale(paymentMethod: method);
    } else if (result == 'confirm_with_change') {
      // 잔돈 계산과 함께 판매 확정
      _completeSale(paymentMethod: method, showChangeCalculator: true);
    }
  }

  // 판매 완료 처리 (배치 처리 + 백그라운드 동기화)
  Future<void> _completeSale({String? paymentMethod, bool showChangeCalculator = false}) async {
    try {
      LoggerUtils.logDebug('배치 판매 처리 시작', tag: 'SaleScreen');
      final quantities = _productQuantitiesNotifier.value;
      final serviceQuantities = _serviceQuantitiesNotifier.value;
      LoggerUtils.logDebug('선택된 상품 수: ${quantities.length}', tag: 'SaleScreen');
      LoggerUtils.logDebug('서비스 상품 수: ${serviceQuantities.length}', tag: 'SaleScreen');

      // 배치 처리를 위한 데이터 준비
      final productsToUpdate = <Product>[];
      final salesLogsToAdd = <SalesLog>[];

      // 배치 ID 생성 (여러 상품이 있거나 하나의 상품이라도 수량이 1개 이상이면 배치로 처리)
      final totalItems = quantities.values.fold<int>(0, (sum, qty) => sum + qty) +
                        serviceQuantities.values.fold<int>(0, (sum, qty) => sum + qty);
      final batchSaleId = totalItems > 0 ? 'batch_${DateTime.now().microsecondsSinceEpoch}' : null;

      final allProducts = ref
          .watch(productNotifierProvider)
          .products
          .where((product) => product.isActive)
          .toList();

      // 세트 할인 계산 (플랜 제한 확인)
      SetDiscountResult setDiscountResult = SetDiscountResult.empty();
      try {
        final hasSetDiscountFeature = await SubscriptionUtils.hasFeature(ref, 'setDiscount');

        if (hasSetDiscountFeature) {
          final activeSetDiscounts = ref.read(activeSetDiscountsProvider);
          final totalAmount = quantities.entries
              .map((e) => allProducts.firstWhere((p) => p.id == e.key).price * e.value)
              .fold<int>(0, (sum, amount) => sum + amount);

          // 수동 할인 적용 후 금액 계산
          int totalAfterManualDiscount = totalAmount;
          if (_manualDiscountEnabledNotifier.value && _manualDiscountAmountNotifier.value > 0) {
            totalAfterManualDiscount = totalAmount - _manualDiscountAmountNotifier.value;
            if (totalAfterManualDiscount < 0) totalAfterManualDiscount = 0;
          }

          setDiscountResult = SetDiscountService.calculateOptimalDiscount(
            quantities,
            activeSetDiscounts,
            allProducts: allProducts,
            totalAmount: totalAmount, // 할인 적용 전 금액
            totalAmountAfterOtherDiscounts: totalAfterManualDiscount, // 다른 할인 적용 후 금액
          );
          LoggerUtils.logDebug('세트 할인 계산 완료: ${setDiscountResult.totalDiscountAmount}원', tag: 'SaleScreen');
        }
      } catch (e) {
        LoggerUtils.logError('세트 할인 계산 실패', error: e, tag: 'SaleScreen');
      }

      // 1단계: 상품 데이터 준비 및 검증
      for (final entry in quantities.entries) {
        if (entry.value > 0) {
          final product = allProducts.firstWhere((p) => p.id == entry.key);

          if (product.quantity >= entry.value) {
            // 재고 차감된 상품 준비
            final updatedProduct = product.copyWith(
              quantity: product.quantity - entry.value,
            );
            productsToUpdate.add(updatedProduct);

            // 세트 할인 정보 계산
            int productSetDiscountAmount = 0;
            String? productSetDiscountNames;

            if (setDiscountResult.totalDiscountAmount > 0 &&
                setDiscountResult.usedProductQuantities.containsKey(product.id)) {
              // 세트 할인이 적용된 상품인지 확인하고 할인 금액 계산
              final appliedDiscountNames = <String>[];
              int totalDiscountForProduct = 0;

              for (final applied in setDiscountResult.appliedDiscounts) {
                bool isProductIncluded = false;

                switch (applied.setDiscount.conditionType) {
                  case SetDiscountConditionType.productCombination:
                    isProductIncluded = applied.setDiscount.productIds.contains(product.id);
                    break;
                  case SetDiscountConditionType.minimumAmount:
                    isProductIncluded = true;
                    break;
                  case SetDiscountConditionType.categoryQuantity:
                    if (applied.setDiscount.categoryCondition != null) {
                      isProductIncluded = product.categoryId == applied.setDiscount.categoryCondition!.categoryId;
                    }
                    break;
                  case SetDiscountConditionType.productGroupQuantity:
                    if (applied.setDiscount.productGroupCondition != null) {
                      isProductIncluded = applied.setDiscount.productGroupCondition!.productIds.contains(product.id);
                    }
                    break;
                }

                if (isProductIncluded) {
                  appliedDiscountNames.add(applied.setDiscount.name);

                  // 할인 금액을 상품별로 분배
                  final totalDiscountAmount = applied.setDiscount.discountAmount * applied.appliedCount;

                  if (applied.setDiscount.conditionType == SetDiscountConditionType.productCombination) {
                    // 상품 조합 할인: 조합에 포함된 상품들에게 균등 분배 (나머지 처리)
                    final productCount = applied.setDiscount.productIds.length;
                    final baseDiscount = totalDiscountAmount ~/ productCount;
                    final remainder = totalDiscountAmount % productCount;

                    // 현재 상품이 조합에서 몇 번째인지 확인 (가장 비싼 상품에 나머지 추가)
                    final sortedProductIds = applied.setDiscount.productIds.toList()
                      ..sort((a, b) {
                        final priceA = allProducts.firstWhere((p) => p.id == a).price;
                        final priceB = allProducts.firstWhere((p) => p.id == b).price;
                        return priceB.compareTo(priceA); // 내림차순 (비싼 것부터)
                      });

                    final isFirstProduct = sortedProductIds.first == product.id;
                    totalDiscountForProduct += baseDiscount + (isFirstProduct ? remainder : 0);
                  } else if (applied.setDiscount.conditionType == SetDiscountConditionType.minimumAmount) {
                    // 최소 금액 할인: 전체 구매 금액 비율로 분배 (나머지 처리)
                    final itemTotal = product.price * entry.value;
                    final totalOriginalAmount = quantities.entries
                        .map((e) => allProducts.firstWhere((p) => p.id == e.key).price * e.value)
                        .fold<int>(0, (sum, amount) => sum + amount);
                    if (totalOriginalAmount > 0) {
                      // 비율 계산 후 정수로 변환 (나머지는 가장 비싼 상품에 몰아주기)
                      final exactDiscount = (totalDiscountAmount * itemTotal / totalOriginalAmount);
                      final baseDiscount = exactDiscount.floor();

                      // 현재 상품이 가장 비싼 상품인지 확인
                      final sortedProducts = quantities.entries.toList()
                        ..sort((a, b) {
                          final priceA = allProducts.firstWhere((p) => p.id == a.key).price * a.value;
                          final priceB = allProducts.firstWhere((p) => p.id == b.key).price * b.value;
                          return priceB.compareTo(priceA); // 내림차순 (비싼 것부터)
                        });

                      final isMostExpensive = sortedProducts.first.key == product.id;

                      // 나머지 계산 (전체 할인액에서 각 상품별 기본 할인액을 뺀 값)
                      if (isMostExpensive) {
                        final totalBaseDiscount = quantities.entries
                            .map((e) => ((totalDiscountAmount * (allProducts.firstWhere((p) => p.id == e.key).price * e.value) / totalOriginalAmount)).floor())
                            .fold<int>(0, (sum, discount) => sum + discount);
                        final remainder = totalDiscountAmount - totalBaseDiscount;
                        totalDiscountForProduct += baseDiscount + remainder;
                      } else {
                        totalDiscountForProduct += baseDiscount;
                      }
                    }
                  } else {
                    // 카테고리/그룹 할인: 해당 카테고리/그룹 내에서 균등 분배 (중복 방지)
                    final relevantProducts = allProducts.where((p) {
                      if (applied.setDiscount.conditionType == SetDiscountConditionType.categoryQuantity) {
                        return p.categoryId == applied.setDiscount.categoryCondition!.categoryId &&
                               quantities.containsKey(p.id);
                      } else {
                        return applied.setDiscount.productGroupCondition!.productIds.contains(p.id) &&
                               quantities.containsKey(p.id);
                      }
                    }).length;
                    if (relevantProducts > 0) {
                      // 반복 적용 횟수를 반영하여 할인 금액 계산 후 상품 수로 나누어 분배
                      final totalDiscountForCategory = applied.setDiscount.discountAmount * applied.appliedCount;
                      totalDiscountForProduct += totalDiscountForCategory ~/ relevantProducts;
                    }
                  }
                }
              }

              if (appliedDiscountNames.isNotEmpty) {
                productSetDiscountAmount = totalDiscountForProduct;
                productSetDiscountNames = appliedDiscountNames.join(', ');
              }
            }

            // 수동 할인 계산 (나머지 처리)
            int productManualDiscountAmount = 0;
            if (_manualDiscountEnabledNotifier.value && _manualDiscountAmountNotifier.value > 0) {
              final itemTotal = product.price * entry.value;
              final totalOriginalAmount = quantities.entries
                  .map((e) => allProducts.firstWhere((p) => p.id == e.key).price * e.value)
                  .fold<int>(0, (sum, amount) => sum + amount);

              if (totalOriginalAmount > 0) {
                // 비율 계산 후 정수로 변환 (나머지는 가장 비싼 상품에 몰아주기)
                final exactDiscount = (_manualDiscountAmountNotifier.value * itemTotal / totalOriginalAmount);
                final baseDiscount = exactDiscount.floor();

                // 현재 상품이 가장 비싼 상품인지 확인
                final sortedProducts = quantities.entries.toList()
                  ..sort((a, b) {
                    final priceA = allProducts.firstWhere((p) => p.id == a.key).price * a.value;
                    final priceB = allProducts.firstWhere((p) => p.id == b.key).price * b.value;
                    return priceB.compareTo(priceA); // 내림차순 (비싼 것부터)
                  });

                final isMostExpensive = sortedProducts.first.key == product.id;

                // 나머지 계산
                if (isMostExpensive) {
                  final totalBaseDiscount = quantities.entries
                      .map((e) => ((_manualDiscountAmountNotifier.value * (allProducts.firstWhere((p) => p.id == e.key).price * e.value) / totalOriginalAmount)).floor())
                      .fold<int>(0, (sum, discount) => sum + discount);
                  final remainder = _manualDiscountAmountNotifier.value - totalBaseDiscount;
                  productManualDiscountAmount = baseDiscount + remainder;
                } else {
                  productManualDiscountAmount = baseDiscount;
                }
              }
            }

            // 정가로 판매 금액 계산 (할인은 별도 저장)
            final itemTotal = product.price * entry.value;

            // 카테고리명 가져오기
            String? categoryName;
            try {
              final categories = ref.read(categoryNotifierProvider).value ?? [];
              final category = categories.firstWhere(
                (cat) => cat.id == product.categoryId,
              );
              categoryName = category.name;
            } catch (e) {
              // 카테고리를 찾을 수 없는 경우 null로 유지
              categoryName = null;
            }

            // 판매 로그 생성 (정가로 기록)
            final salesLog = SalesLog.create(
              id: 0,
              productId: product.id,
              productName: product.name,
              categoryName: categoryName, // 카테고리명 추가
              soldPrice: product.price,
              soldQuantity: entry.value,
              totalAmount: itemTotal, // 정가로 기록
              sellerName: product.sellerName,
              transactionType: TransactionType.sale,
              batchSaleId: batchSaleId,
              setDiscountAmount: productSetDiscountAmount,
              setDiscountNames: productSetDiscountNames,
              manualDiscountAmount: productManualDiscountAmount,
            ).copyWith(paymentMethod: paymentMethod);

            salesLogsToAdd.add(salesLog);

            LoggerUtils.logDebug('상품 준비 완료: ${product.name} x${entry.value}개', tag: 'SaleScreen');
          } else {
            // 재고 부족 처리
            if (mounted) {
              ToastUtils.showToast(
                context,
                '${product.name} 재고 부족 (현재 ${product.quantity}개)',
              );
            }
            return; // 재고 부족 시 전체 판매 중단
          }
        }
      }

      // 2단계: 서비스 데이터 준비
      for (final entry in serviceQuantities.entries) {
        if (entry.value > 0) {
          final product = allProducts.firstWhere((p) => p.id == entry.key);

          if (product.quantity >= entry.value) {
            // 서비스는 재고 차감만 하고 판매 로그는 별도 처리
            final updatedProduct = product.copyWith(
              quantity: product.quantity - entry.value,
            );
            productsToUpdate.add(updatedProduct);

            // 카테고리명 가져오기
            String? categoryName;
            try {
              final categories = ref.read(categoryNotifierProvider).value ?? [];
              final category = categories.firstWhere(
                (cat) => cat.id == product.categoryId,
              );
              categoryName = category.name;
            } catch (e) {
              // 카테고리를 찾을 수 없는 경우 null로 유지
              categoryName = null;
            }

            // 서비스 로그 생성
            final serviceLog = SalesLog.create(
              id: 0,
              productId: product.id,
              productName: product.name,
              categoryName: categoryName, // 카테고리명 추가
              soldPrice: 0, // 서비스는 가격 0
              soldQuantity: entry.value,
              totalAmount: 0,
              sellerName: product.sellerName,
              transactionType: TransactionType.service,
              batchSaleId: batchSaleId,
            ).copyWith(paymentMethod: paymentMethod);

            salesLogsToAdd.add(serviceLog);

            LoggerUtils.logDebug('서비스 준비 완료: ${product.name} x${entry.value}개', tag: 'SaleScreen');
          } else {
            // 재고 부족 처리
            if (mounted) {
              ToastUtils.showToast(
                context,
                '${product.name} 재고 부족 (현재 ${product.quantity}개)',
              );
            }
            return; // 재고 부족 시 전체 판매 중단
          }
        }
      }

      // 3단계: 배치 처리 실행
      LoggerUtils.logDebug('배치 처리 시작: 상품 ${productsToUpdate.length}개, 판매로그 ${salesLogsToAdd.length}개', tag: 'SaleScreen');

      // 병렬 처리: 상품 업데이트와 판매 로그 저장을 동시에 수행
      final futures = <Future>[];

      // 상품 배치 업데이트
      if (productsToUpdate.isNotEmpty) {
        futures.add(
          ref.read(productNotifierProvider.notifier).batchUpdateProducts(productsToUpdate)
        );
      }

      // 판매 로그 배치 저장
      if (salesLogsToAdd.isNotEmpty) {
        futures.add(
          ref.read(salesLogNotifierProvider.notifier).batchAddSalesLogs(salesLogsToAdd)
        );
      }

      // 세트 할인 거래 정보 저장
      if (setDiscountResult.totalDiscountAmount > 0) {
        final currentWorkspace = ref.read(unifiedWorkspaceProvider).currentWorkspace;
        if (currentWorkspace != null) {
          futures.add(_saveSetDiscountTransaction(setDiscountResult, batchSaleId, currentWorkspace.id));
        }
      }

      // 모든 배치 처리 완료 대기
      await Future.wait(futures);

      LoggerUtils.logDebug('배치 처리 완료', tag: 'SaleScreen');

      // 4단계: 현금 결제이고 잔돈 계산이 요청된 경우 거스름돈 계산 다이얼로그 표시 (UI 초기화 전에)
      if (mounted && paymentMethod == 'cash' && showChangeCalculator) {
        final totalAmount = _totalAmountNotifier.value;
        await ChangeCalculatorDialog.show(
          context: context,
          totalAmount: totalAmount,
        );
      }

      // 5단계: UI 상태 먼저 초기화 (중복 표시 방지)
      if (mounted) {
        _productQuantitiesNotifier.value = {};
        _serviceQuantitiesNotifier.value = {};
        _totalAmountNotifier.value = 0;
        _setDiscountInfoNotifier.value = null;
        _manualDiscountEnabledNotifier.value = false;
        _manualDiscountAmountNotifier.value = 0;
        LoggerUtils.logDebug('상태 초기화 완료', tag: 'SaleScreen');
      }

      // 6단계: 상품 목록 새로고침 (재고 변화 반영)
      if (mounted) {
        await ref.read(productNotifierProvider.notifier).loadProducts(showLoading: false);
        LoggerUtils.logDebug('상품 목록 새로고침 완료', tag: 'SaleScreen');
      }

      // 6.5단계: 통계 갱신 트리거 (세트 할인 통계 실시간 갱신)
      if (mounted) {
        try {
          final currentRefresh = ref.read(statisticsRefreshProvider);
          ref.read(statisticsRefreshProvider.notifier).state = currentRefresh + 1;
          LoggerUtils.logDebug('통계 갱신 트리거 완료', tag: 'SaleScreen');
        } catch (e) {
          LoggerUtils.logError('통계 갱신 트리거 실패', error: e, tag: 'SaleScreen');
        }
      }

      // 7단계: 성공 메시지 표시
      if (mounted) {
        ToastUtils.showToast(
          context,
          '판매가 성공적으로 처리되었습니다.',
        );
      }
    } catch (e) {
      LoggerUtils.logError('배치 판매 처리 실패', error: e, tag: 'SaleScreen');
      if (mounted) {
        ToastUtils.showToast(
          context,
          '판매 처리 중 오류가 발생했습니다: ${e.toString()}',
        );
      }
    } finally {
      LoggerUtils.logDebug('판매 처리 최종 완료', tag: 'SaleScreen');
    }
  }

  /// 세트 할인 거래 정보 저장
  Future<void> _saveSetDiscountTransaction(
    SetDiscountResult setDiscountResult,
    String? batchSaleId,
    int eventId,
  ) async {
    try {
      if (batchSaleId == null) return;

      // AppliedSetDiscount를 AppliedSetDiscountData로 변환
      final appliedDiscountsData = setDiscountResult.appliedDiscounts
          .map((applied) => AppliedSetDiscountData.fromAppliedSetDiscount(applied))
          .toList();

      // 실제 세트 할인 적용 횟수 계산
      final totalAppliedCount = setDiscountResult.appliedDiscounts
          .fold<int>(0, (sum, applied) => sum + applied.appliedCount);

      // 세트 할인 거래 정보 생성
      final transaction = SetDiscountTransaction.create(
        batchSaleId: batchSaleId,
        appliedDiscounts: appliedDiscountsData,
        totalDiscountAmount: setDiscountResult.totalDiscountAmount,
        appliedCount: totalAppliedCount,
        eventId: eventId,
      );

      // 데이터베이스에 저장
      final repository = SetDiscountTransactionRepository();
      final savedId = await repository.insert(transaction);

      // 로컬 전용 모드: Firebase 업로드 불필요
      LoggerUtils.logDebug('로컬 전용 모드: 세트 할인 거래 서버 업로드 건너뜀: ID $savedId', tag: 'SaleScreen');

      LoggerUtils.logDebug(
        '세트 할인 거래 정보 저장 완료: ${transaction.totalDiscountAmount}원, ${transaction.appliedCount}회',
        tag: 'SaleScreen',
      );
    } catch (e) {
      LoggerUtils.logError('세트 할인 거래 정보 저장 실패', error: e, tag: 'SaleScreen');
    }
  }

  /// 카테고리 관리 다이얼로그 표시
  void _showCategoryManagementDialog() {
    showDialog(
      context: context,
      builder: (context) => _CategoryManagementDialog(),
    );
  }

  /// 세트 할인 관리 다이얼로그 표시
  Future<void> _showSetDiscountManagementDialog() async {
    // 세트 할인 기능 사용 권한 확인
    final hasAccess = await SubscriptionUtils.checkFeatureAccess(
      ref: ref,
      context: context,
      featureName: 'setDiscount',
    );

    if (hasAccess) {
      SetDiscountDialog.show(context);
    }
  }

  /// UI 열 수 조정 다이얼로그 표시
  void _showUIColumnsSettings() async {
    await showDialog(
      context: context,
      builder: (context) => Consumer(
        builder: (context, ref, child) {
          final settingsState = ref.watch(settingsNotifierProvider);

          return settingsState.when(
            loading: () => const AlertDialog(
              title: Text('UI 열 수 설정'),
              content: Center(child: CircularProgressIndicator()),
            ),
            error: (error, stack) => AlertDialog(
              title: const Text('UI 열 수 설정'),
              content: Text('설정을 불러오는 중 오류가 발생했습니다: $error'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('확인'),
                ),
              ],
            ),
            data: (settings) => _UIColumnsSettingsDialog(
              portraitColumns: settings.inventoryColumnsPortrait,
              landscapeColumns: settings.inventoryColumnsLandscape,
              onPortraitColumnsChanged: (value) async {
                LoggerUtils.logInfo('세로모드 열 수 변경: $value', tag: 'SaleScreen');
                await ref.read(settingsNotifierProvider.notifier).setInventoryColumnsPortrait(value);
                LoggerUtils.logInfo('세로모드 열 수 변경 완료', tag: 'SaleScreen');
              },
              onLandscapeColumnsChanged: (value) async {
                LoggerUtils.logInfo('가로모드 열 수 변경: $value', tag: 'SaleScreen');
                await ref.read(settingsNotifierProvider.notifier).setInventoryColumnsLandscape(value);
                LoggerUtils.logInfo('가로모드 열 수 변경 완료', tag: 'SaleScreen');
              },
            ),
          );
        },
      ),
    );
  }

  /// 결제수단 관리 다이얼로그 표시
  void _showPaymentMethodsDialog() {
    showDialog(
      context: context,
      builder: (_) => const PaymentMethodsDialog(),
    );
  }

  /// 대량등록 화면을 표시합니다.
  void _showBulkRegistrationScreen() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ManualBulkProductRegistrationScreen(),
      ),
    );
  }
  /// 주문 데이터 초기화
  void _refreshOrderData() {
    setState(() {
      // 선택 초기화
      _productQuantitiesNotifier.value = {};
      _serviceQuantitiesNotifier.value = {};

      // 수동 할인 초기화
      _manualDiscountEnabledNotifier.value = false;
      _manualDiscountAmountNotifier.value = 0;
    });

    // 총 금액 재계산하여 UI 즉시 업데이트
    _calculateTotal();
  }






  /// 정렬 및 필터링 다이얼로그 표시
  void _showSortAndFilterDialog() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    showDialog(
      context: context,
      builder: (context) => Consumer(
        builder: (context, ref, child) {
          final currentOption = ref.watch(productNotifierProvider).currentSortOption;

          return custom_dialog.DialogTheme.buildResponsiveDialog(
            isCompact: true,
            child: Padding(
              padding: custom_dialog.DialogTheme.getCompactDialogPadding(isTablet),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxHeight: custom_dialog.DialogTheme.getResponsiveMaxHeight(context),
                ),
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                  // 제목
                  Row(
                    children: [
                      Icon(
                        Icons.analytics,
                        size: isTablet ? 20 : 18,
                        color: AppColors.primarySeed,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '정렬 및 필터',
                          style: custom_dialog.DialogTheme.titleStyle.copyWith(
                            fontSize: isTablet ? 20.0 : 18.0,
                          ),
                        ),
                      ),
                      // 우측 상단 체크 버튼
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: Icon(
                          Icons.check,
                          color: AppColors.primarySeed,
                          size: isTablet ? 24 : 20,
                        ),
                        tooltip: '완료',
                      ),
                    ],
                  ),
                  SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),

                  // 내용
                  Container(
                    constraints: BoxConstraints(
                      maxHeight: MediaQuery.of(context).size.height * 0.6,
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 판매자 필터 섹션
                          Consumer(
                            builder: (context, ref, child) {
                              final sellerAsync = ref.watch(sellerNotifierProvider);
                              // 로컬 데이터 우선 사용 (로딩 상태 무시)
                              final sellerNames = sellerAsync.sellers.map((s) => s.name).toList()..sort();
                              final sellers = ['전체 판매자', ...sellerNames];

                              final currentSellerFilter = ref.watch(productNotifierProvider.select((state) => state.selectedSellerFilter));
                              final displayValue = currentSellerFilter.isEmpty ? '전체 판매자' : currentSellerFilter;

                              return Row(
                                children: [
                                  Text(
                                    '판매자 필터',
                                    style: TextStyle(
                                      fontSize: isTablet ? 16 : 14,
                                      fontWeight: FontWeight.w600,
                                      color: AppColors.onSurface,
                                      fontFamily: 'Pretendard',
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: DropdownButtonHideUnderline(
                                      child: DropdownButton2<String>(
                                        value: displayValue,
                                        isExpanded: true,
                                        hint: Text(
                                          '판매자 선택',
                                          style: TextStyle(
                                            fontFamily: 'Pretendard',
                                            fontSize: isTablet ? 16 : 14,
                                            color: AppColors.onSurfaceVariant,
                                          ),
                                        ),
                                        style: TextStyle(
                                          fontFamily: 'Pretendard',
                                          fontSize: isTablet ? 16 : 14,
                                          color: AppColors.onSurface,
                                        ),
                                        iconStyleData: IconStyleData(
                                          icon: Icon(
                                            Icons.arrow_drop_down,
                                            color: AppColors.onSurfaceVariant,
                                          ),
                                        ),
                                        buttonStyleData: ButtonStyleData(
                                          height: isTablet ? 50 : 46,
                                          padding: EdgeInsets.symmetric(
                                            horizontal: isTablet ? 16 : 12,
                                          ),
                                          decoration: BoxDecoration(
                                            border: Border.all(color: AppColors.onSurfaceVariant),
                                            borderRadius: BorderRadius.circular(12),
                                            color: AppColors.surface,
                                          ),
                                        ),
                                        dropdownStyleData: DropdownStyleData(
                                          maxHeight: 200,
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            borderRadius: BorderRadius.circular(8),
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.black.withValues(alpha: 0.1),
                                                blurRadius: 8,
                                                offset: const Offset(0, 4),
                                              ),
                                            ],
                                          ),
                                        ),
                                        menuItemStyleData: MenuItemStyleData(
                                          height: isTablet ? 50 : 46,
                                          padding: EdgeInsets.symmetric(horizontal: isTablet ? 16 : 12),
                                        ),
                                        items: sellers.map((seller) => DropdownMenuItem<String>(
                                          value: seller,
                                          child: Text(
                                            seller,
                                            style: TextStyle(
                                              fontFamily: 'Pretendard',
                                              fontSize: isTablet ? 16 : 14,
                                              color: AppColors.onSurface,
                                            ),
                                          ),
                                        )).toList(),
                                        onChanged: (value) {
                                          if (value != null) {
                                            final filterValue = value == '전체 판매자' ? '' : value;
                                            ref.read(productNotifierProvider.notifier).setSellerFilter(filterValue);
                                          }
                                        },
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                          SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),

                          // 정렬 방식 섹션
                          Text(
                            '정렬 방식',
                            style: TextStyle(
                              fontSize: isTablet ? 16 : 14,
                              fontWeight: FontWeight.w600,
                              color: AppColors.onSurface,
                              fontFamily: 'Pretendard',
                            ),
                          ),
                          const SizedBox(height: 8),

                          // 정렬 옵션들
                          _buildPOSSortTile(
                            context: context,
                            title: '등록순',
                            isSelected: currentOption == ProductSortOption.recentlyAdded,
                            isAscending: false,
                            isTablet: isTablet,
                            onTap: () {
                              ref.read(productNotifierProvider.notifier).setSortOption(ProductSortOption.recentlyAdded);
                            },
                          ),
                          _buildPOSSortTile(
                            context: context,
                            title: '이름순',
                            isSelected: currentOption == ProductSortOption.nameAsc || currentOption == ProductSortOption.nameDesc,
                            isAscending: currentOption == ProductSortOption.nameAsc,
                            isTablet: isTablet,
                            onTap: () {
                              final newOption = currentOption == ProductSortOption.nameAsc
                                  ? ProductSortOption.nameDesc
                                  : ProductSortOption.nameAsc;
                              ref.read(productNotifierProvider.notifier).setSortOption(newOption);
                            },
                          ),
                          _buildPOSSortTile(
                            context: context,
                            title: '가격순',
                            isSelected: currentOption == ProductSortOption.priceAsc || currentOption == ProductSortOption.priceDesc,
                            isAscending: currentOption == ProductSortOption.priceAsc,
                            isTablet: isTablet,
                            onTap: () {
                              final newOption = currentOption == ProductSortOption.priceAsc
                                  ? ProductSortOption.priceDesc
                                  : ProductSortOption.priceAsc;
                              ref.read(productNotifierProvider.notifier).setSortOption(newOption);
                            },
                          ),
                          _buildPOSSortTile(
                            context: context,
                            title: '재고순',
                            isSelected: currentOption == ProductSortOption.quantityAsc || currentOption == ProductSortOption.quantityDesc,
                            isAscending: currentOption == ProductSortOption.quantityAsc,
                            isTablet: isTablet,
                            onTap: () {
                              final newOption = currentOption == ProductSortOption.quantityAsc
                                  ? ProductSortOption.quantityDesc
                                  : ProductSortOption.quantityAsc;
                              ref.read(productNotifierProvider.notifier).setSortOption(newOption);
                            },
                          ),
                        ],
                      ),
                    ),
                  ),


                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// POS 스타일 정렬 타일 빌더 (테두리 방식)
  Widget _buildPOSSortTile({
    required BuildContext context,
    required String title,
    required bool isSelected,
    required bool isAscending,
    required bool isTablet,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: isTablet ? 6 : 4),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: onTap,
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: isTablet ? 16 : 12,
              vertical: isTablet ? 14 : 12,
            ),
            decoration: isSelected ? BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.primarySeed,
                width: 2,
              ),
            ) : null,
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontFamily: 'Pretendard',
                      fontSize: isTablet ? 16 : 14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.onSurface,
                    ),
                  ),
                ),
                if (isSelected && title != '등록순') // 등록순은 화살표 없음
                  Icon(
                    isAscending ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                    color: AppColors.primarySeed,
                    size: isTablet ? 20 : 18,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 상품 그리드 위젯 - 깜빡임 방지를 위한 별도 위젯
class _ProductGrid extends ConsumerWidget {
  final ValueNotifier<Map<int, int>> productQuantitiesNotifier;
  final Function(Product) onProductTap;
  final Function(Product)? onProductLongPress;
  final Function(Product)? onProductDecrease;
  final Function(Sale)? onSaleTap;
  final Function(Sale)? onSaleLongPress;
  final bool isEditMode;
  final bool isOrderPanelVisible;
  final bool isDeletionMode; // 삭제 모드 여부
  final Set<int> selectedProductsForDeletion; // 삭제용 선택된 상품들
  final Function(int)? onProductCheckToggle; // 상품 체크 토글 콜백

  const _ProductGrid({
    required this.productQuantitiesNotifier,
    required this.onProductTap,
    this.onProductLongPress,
    this.onProductDecrease,
    this.onSaleTap,
    this.onSaleLongPress,
    this.isEditMode = false,
    this.isOrderPanelVisible = false,
    this.isDeletionMode = false,
    required this.selectedProductsForDeletion,
    this.onProductCheckToggle,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 상품 데이터를 watch로 가져와서 실시간 업데이트
    final productState = ref.watch(productNotifierProvider);

    // 로딩 상태는 개별 이미지에서 처리됨

    // 실시간으로 필터링된 상품 목록 사용 (정렬 적용된 filteredProducts 사용)
    // 품절 상품도 표시하여 정렬 효과를 확인할 수 있도록 함 (isActive 필터 제거)
    List<Product> allProducts = productState.filteredProducts;

    final orientation = MediaQuery.of(context).orientation;
    final isPortrait = orientation == Orientation.portrait;

    // 화면 방향에 따라 설정 Provider의 값을 직접 사용 (watch 대신 read 사용)
    final columns = isPortrait
        ? ref.watch(saleColumnsPortraitProvider)
        : ref.watch(saleColumnsLandscapeProvider);

    // 화면 크기 정보
    final screenWidth = MediaQuery.of(context).size.width;

    // 카드 크기는 항상 전체 화면 기준으로 계산 (주문 패널 상태 무시)
    // 이렇게 하면 주문 패널이 열려도 카드 크기가 변하지 않음
    final cardSize = DeviceUtils.getFixedCardSize(context, columns);

    // Wrap 컨테이너의 사용 가능한 너비만 주문 패널 상태에 따라 조정
    final containerWidth = (!isPortrait && isOrderPanelVisible && !isEditMode)
        ? screenWidth - 388  // 주문 패널이 열려있을 때: 공간만 줄임
        : screenWidth;       // 주문 패널이 닫혀있을 때: 전체 공간 사용

    // 디버깅 정보 출력
    final deviceInfo = Dimens.getDeviceInfo(context);
    LoggerUtils.logDebug('Sale Screen - Device Info: $deviceInfo', tag: 'SaleScreen');
    LoggerUtils.logDebug('Sale Screen - Orientation: ${isPortrait ? "Portrait" : "Landscape"}', tag: 'SaleScreen');
    LoggerUtils.logDebug('Sale Screen - Columns: $columns', tag: 'SaleScreen');

    // 로컬 DB에서 빠르게 상품 그리드 표시 (이미지만 개별 스켈레톤 로딩)
    final showImages = ref.watch(showProductImagesProvider);
    return _buildCategoryGroupedProducts(allProducts, columns, cardSize, containerWidth, ref, showImages);
  }



  /// 카테고리별로 그룹화된 상품 목록을 빌드
  Widget _buildCategoryGroupedProducts(List<Product> products, int columns, Size cardSize, double containerWidth, WidgetRef ref, bool showImages) {
    // 카테고리별로 상품 그룹화
    final Map<int, List<Product>> groupedProducts = {};
    for (final product in products) {
      final categoryId = product.categoryId;
      if (!groupedProducts.containsKey(categoryId)) {
        groupedProducts[categoryId] = [];
      }
      groupedProducts[categoryId]!.add(product);
    }

    // 디버깅: 상품별 카테고리 ID 로깅
    for (final product in products) {
      LoggerUtils.logDebug('상품 "${product.name}" - categoryId: ${product.categoryId}', tag: 'SaleScreen');
    }
    LoggerUtils.logDebug('그룹화된 카테고리: ${groupedProducts.keys.toList()}', tag: 'SaleScreen');

    // 카테고리 데이터 가져오기
    final categoriesAsyncValue = ref.watch(categoryNotifierProvider);

    return categoriesAsyncValue.when(
      loading: () => const SizedBox.shrink(), // 카테고리 로딩 시 아무것도 표시하지 않음
      error: (error, stackTrace) => Center(child: Text('카테고리 로딩 오류: $error')),
      data: (categories) {
        // 모든 카테고리를 정렬 순서에 따라 표시 (상품이 없어도 표시)
        final sortedCategories = [...categories]
          ..sort((a, b) => a.sortOrder.compareTo(b.sortOrder));

        LoggerUtils.logDebug('전체 카테고리 수: ${categories.length}', tag: 'SaleScreen');
        for (final category in categories) {
          final productCount = groupedProducts[category.id]?.length ?? 0;
          LoggerUtils.logDebug('카테고리 "${category.name}" (ID: ${category.id}) - 상품 수: $productCount', tag: 'SaleScreen');
        }

        return AnimatedSwitcher(
          duration: const Duration(milliseconds: 400),
          child: Align(
            alignment: Alignment.topCenter,
            child: SingleChildScrollView( // 오버플로우 방지를 위한 스크롤 래퍼
              key: ValueKey('sale_categories_${sortedCategories.length}'),
              padding: const EdgeInsets.all(8.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: sortedCategories.map((category) {
                  final categoryProducts = groupedProducts[category.id] ?? []; // 빈 리스트도 허용

                  return AnimatedContainer(
                    key: ValueKey('sale_category_${category.id}'),
                    duration: const Duration(milliseconds: 250),
                    curve: Curves.easeInOut,
                    child: _SaleCategorySection(
                      category: category,
                      products: categoryProducts,
                      columns: columns,
                      cardSize: cardSize,
                      containerWidth: containerWidth,
                      productQuantitiesNotifier: productQuantitiesNotifier,
                      onProductTap: onProductTap,
                      onProductLongPress: onProductLongPress,
                      onProductDecrease: onProductDecrease,
                      isEditMode: isEditMode,
                      isOrderPanelVisible: isOrderPanelVisible,
                      isDeletionMode: isDeletionMode,
                      selectedProductsForDeletion: selectedProductsForDeletion,
                      onProductCheckToggle: onProductCheckToggle,
                      showImages: showImages, // 이미지 표시 여부 전달
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        );
      },
    );
  }


}



/// 판매 화면용 카테고리별 상품 섹션 위젯
class _SaleCategorySection extends StatelessWidget {
  // GlobalKey 보관함 - 번호판을 한 번만 만들어서 재사용 🚗
  static final Map<int, GlobalKey> _productKeys = {};
  static final Map<String, GlobalKey> _buttonKeys = {};

  final model_category.Category category;
  final List<Product> products;
  final int columns;
  final Size cardSize;
  final double containerWidth;
  final ValueNotifier<Map<int, int>> productQuantitiesNotifier;
  final Function(Product) onProductTap;
  final Function(Product)? onProductLongPress;
  final Function(Product)? onProductDecrease;
  final bool isEditMode;
  final bool isOrderPanelVisible; // 주문 패널 상태 추가
  final bool isDeletionMode; // 삭제 모드 여부
  final Set<int> selectedProductsForDeletion; // 삭제용 선택된 상품들
  final Function(int)? onProductCheckToggle; // 상품 체크 토글 콜백
  final bool showImages; // 이미지 표시 여부

  const _SaleCategorySection({
    required this.category,
    required this.products,
    required this.columns,
    required this.cardSize,
    required this.containerWidth,
    required this.productQuantitiesNotifier,
    required this.onProductTap,
    this.onProductLongPress,
    this.onProductDecrease,
    this.isEditMode = false,
    this.isOrderPanelVisible = false,
    this.isDeletionMode = false,
    required this.selectedProductsForDeletion,
    this.onProductCheckToggle,
    this.showImages = true, // 기본값: 이미지 표시
  });

  // 사용하지 않는 메서드 제거 - Flutter analyze 경고 해결
  // /// 주문 패널 상태에 따른 상품 그리드 너비 계산
  // double? _getProductGridWidth(BuildContext context) {
  //   if (!isOrderPanelVisible || isEditMode) return null; // 전체 너비 사용
  //
  //   final screenWidth = MediaQuery.of(context).size.width;
  //   final orientation = MediaQuery.of(context).orientation;
  //
  //   if (orientation == Orientation.landscape) {
  //     // 가로모드: 주문 패널 너비(380) + 여백(8) 제외
  //     return screenWidth - 388;
  //   } else {
  //     // 세로모드: 전체 너비 사용 (주문 패널이 아래쪽에 위치)
  //     return null;
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 카테고리 제목 - Material Design 3 Chips 스타일 (상품과 정렬 맞춤)
        Container(
          margin: EdgeInsets.only(
            left: DeviceUtils.getOptimalCardSpacing(context), // 상품 카드와 정렬 맞춤
            right: 16.0,
            bottom: 8.0
          ), // 아래 간격 줄임
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 6.0),
                decoration: BoxDecoration(
                  color: Color(category.color).withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(
                    DeviceUtils.isSmartphone(context) ? 12.0 : 16.0, // 상품 카드와 동일한 라운드
                  ),
                  border: Border.all(
                    color: Color(category.color).withValues(alpha: 0.8),
                    width: 1.5,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 카테고리 아이콘
                    Icon(
                      Icons.folder_outlined,
                      size: 18.0,
                      color: Color(category.color),
                    ),
                    const SizedBox(width: 8.0),
                    Text(
                      category.name,
                      style: TextStyle(
                        fontSize: 14.0,
                        fontWeight: FontWeight.bold,
                        color: Color(category.color),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // 카테고리 내 상품들 - 편집 모드에서 빈 카테고리에는 상품 추가 버튼 표시
        if (products.isEmpty && isEditMode)
          Container(
            padding: const EdgeInsets.only(bottom: 24.0),
            child: Container(
              width: containerWidth, // 주문 패널 상태에 따라 조정된 너비
              child: Wrap(
                alignment: WrapAlignment.start,
                spacing: DeviceUtils.getOptimalCardSpacing(context),
                runSpacing: DeviceUtils.getOptimalCardSpacing(context),
              children: [
                RepaintBoundary(
                  child: SizedBox(
                    key: _buttonKeys.putIfAbsent(
                      'add_product_button_${category.id}',
                      () => GlobalKey(debugLabel: 'add_product_button_${category.id}')
                    ),
                    width: cardSize.width,
                    height: cardSize.height,
                    child: _buildAddProductButton(context, columns),
                  ),
                ),
              ],
              ),
            ),
          )
        else
          AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            child: Align(
              alignment: Alignment.topLeft,
              child: Container(
                key: ValueKey('sale_grid_${products.length}'),
                padding: const EdgeInsets.only(bottom: 24.0), // 카테고리 간 간격 늘림
                child: Container(
                  width: containerWidth, // 주문 패널 상태에 따라 조정된 너비
                  child: Wrap(
                    alignment: WrapAlignment.start,
                    spacing: DeviceUtils.getOptimalCardSpacing(context),
                    runSpacing: DeviceUtils.getOptimalCardSpacing(context),
                  children: [
                    // 상품 아이템들 - 깜빡거림 방지를 위한 RepaintBoundary + 최적화된 GlobalKey 적용
                    for (int index = 0; index < products.length; index++)
                      RepaintBoundary(
                        child: SizedBox(
                          key: products[index].id != null
                            ? _productKeys.putIfAbsent(
                                products[index].id!,
                                () => GlobalKey(debugLabel: 'sale_product_${products[index].id}')
                              )
                            : GlobalKey(debugLabel: 'sale_product_${products[index].name}'),
                          width: showImages ? cardSize.width : cardSize.width,
                          height: showImages ? cardSize.height : cardSize.width, // 텍스트 그리드는 정사각형
                          child: ValueListenableBuilder<Map<int, int>>(
                            valueListenable: productQuantitiesNotifier,
                            builder: (context, quantities, child) {
                              return sale_ui.SaleUiComponents.buildProductGridItem(
                                key: ValueKey(products[index].id),
                                product: products[index],
                                isSelected: quantities[products[index].id] != null && quantities[products[index].id]! > 0,
                                quantity: quantities[products[index].id] ?? 0,
                                onTap: () => onProductTap(products[index]),
                                onLongPress: () => onProductLongPress?.call(products[index]),
                                onDecrease: quantities[products[index].id] != null && quantities[products[index].id]! > 0
                                  ? () => onProductDecrease?.call(products[index])
                                  : null,
                                columns: columns,
                                isEditMode: isEditMode,
                                isDeletionMode: isDeletionMode,
                                isCheckedForDeletion: selectedProductsForDeletion.contains(products[index].id),
                                onCheckToggle: isDeletionMode && products[index].id != null
                                  ? () => onProductCheckToggle?.call(products[index].id!)
                                  : null,
                                showImages: showImages,
                              );
                            },
                          ),
                        ),
                      ),
                    // 편집 모드에서 + 버튼 추가 - 깜빡거림 방지
                    if (isEditMode)
                      RepaintBoundary(
                        child: SizedBox(
                          key: _buttonKeys.putIfAbsent(
                            'add_product_button_edit_${category.id}',
                            () => GlobalKey(debugLabel: 'add_product_button_edit_${category.id}')
                          ),
                          width: cardSize.width,
                          height: showImages ? cardSize.height : cardSize.width, // 텍스트 뷰일 때 정사각형
                          child: _buildAddProductButton(context, columns),
                        ),
                      ),
                  ],
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }

  /// "+ 상품" 버튼 빌더
  Widget _buildAddProductButton(BuildContext context, int columns) {
    final borderRadius = DeviceUtils.isSmartphone(context) ? 12.0 : 16.0;

    return Container(
      margin: DeviceUtils.getCardMargin(context, columns),
      decoration: BoxDecoration(
        border: Border.all(
          color: AppColors.onboardingPrimary.withValues(alpha: 0.3),
          width: 2,
          style: BorderStyle.solid,
        ),
        borderRadius: BorderRadius.circular(borderRadius),
        color: AppColors.surface,
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(borderRadius),
        child: InkWell(
          borderRadius: BorderRadius.circular(borderRadius),
          onTap: () {
            // 상품 등록 화면으로 이동 (해당 카테고리 ID 전달)
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => RegisterProductScreen(
                  initialCategoryId: category.id,
                ),
              ),
            );
          },
          child: Container(
            width: double.infinity,
            height: double.infinity,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.add_circle_outline,
                  size: DeviceUtils.isSmartphone(context) ? 32.0 : 40.0,
                  color: AppColors.onboardingPrimary.withValues(alpha: 0.7),
                ),
                SizedBox(height: 8),
                Text(
                  '상품 추가',
                  style: TextStyle(
                    fontSize: DeviceUtils.isSmartphone(context) ? 12.0 : 14.0,
                    color: AppColors.onboardingPrimary.withValues(alpha: 0.8),
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 카테고리 관리 다이얼로그 - 기존 CategoryManagementScreen과 동일한 기능
class _CategoryManagementDialog extends ConsumerStatefulWidget {
  @override
  ConsumerState<_CategoryManagementDialog> createState() => _CategoryManagementDialogState();
}

class _CategoryManagementDialogState extends ConsumerState<_CategoryManagementDialog> {
  Future<void> _onBackPressed() async {
    if (!_hasChanges) {
      if (mounted) Navigator.of(context).pop();
      return;
    }
    final confirmed = await UnsavedChangesDialog.show(
      context: context,
    );
    if (confirmed == true && mounted) {
      Navigator.of(context).pop();
    }
  }

  List<model_category.Category> _reorderableCategories = [];
  bool _hasChanges = false;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  /// 카테고리 목록 로드
  void _loadCategories() {
    final categories = ref.read(categoryNotifierProvider).maybeWhen(
      data: (categories) => categories,
      orElse: () => <model_category.Category>[],
    );
    setState(() {
      _reorderableCategories = List.from(categories);
      _hasChanges = false; // 초기 로드 시에는 변경사항 없음
    });
  }

  /// 새 카테고리 추가 다이얼로그
  void _showAddCategoryDialog() async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => const _AddCategoryDialog(),
    );

    if (result != null && result['name'] != null && result['name'].isNotEmpty) {
      _addCategoryToMemory(result['name'], result['color']);
    }
  }

  /// 카테고리 리스트 아이템 빌드 (반응형 레이아웃)
  Widget _buildCategoryListItem(model_category.Category category, int index) {
    return Consumer(
      builder: (context, ref, child) {
        final isSmartphone = ref.watch(isSmartphoneDeviceProvider);
        final orientation = MediaQuery.of(context).orientation;
        final isPortrait = orientation == Orientation.portrait;

        // 스마트폰 세로모드에서는 두 줄 레이아웃 사용
        final useVerticalLayout = isSmartphone && isPortrait;

        return _buildCategoryItem(context, category, index, useVerticalLayout, isSmartphone);
      },
    );
  }

  Widget _buildCategoryItem(BuildContext context, model_category.Category category, int index, bool useVerticalLayout, bool isSmartphone) {

    if (useVerticalLayout) {
      return Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 첫 번째 줄: 색상 + 카테고리 이름 + 드래그 핸들
            Row(
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Color(category.color),
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        category.name,
                        style: const TextStyle(
                          fontFamily: 'Pretendard',
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        '순서: ${category.sortOrder + 1}',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
                ReorderableDragStartListener(
                  index: index,
                  child: const Icon(
                    Icons.drag_handle,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            // 두 번째 줄: 버튼들
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  icon: const Icon(Icons.edit, size: 18),
                  label: const Text('수정'),
                  onPressed: () => _showEditCategoryDialog(category),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  icon: const Icon(Icons.delete, size: 18, color: Colors.red),
                  label: const Text('삭제', style: TextStyle(color: Colors.red)),
                  onPressed: () => _showDeleteConfirmDialog(category),
                ),
              ],
            ),
          ],
        ),
      );
    } else {
      // 기존 가로 레이아웃 (타블렛 또는 스마트폰 가로모드)
      return ListTile(
        leading: Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: Color(category.color),
            shape: BoxShape.circle,
          ),
        ),
        title: Text(
          category.name,
          style: const TextStyle(
            fontFamily: 'Pretendard',
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text('순서: ${category.sortOrder + 1}'),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () => _showEditCategoryDialog(category),
              tooltip: '수정',
            ),
            IconButton(
              icon: const Icon(Icons.delete, color: Colors.red),
              onPressed: () => _showDeleteConfirmDialog(category),
              tooltip: '삭제',
            ),
            ReorderableDragStartListener(
              index: index,
              child: const Icon(
                Icons.drag_handle,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }
  }

  /// 카테고리 수정 다이얼로그
  void _showEditCategoryDialog(model_category.Category category) async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => _EditCategoryDialog(category: category),
    );

    if (result != null && result['name'] != null && result['name'].isNotEmpty) {
      _updateCategoryInMemory(category.id!, result['name'], result['color']);
    }
  }

  /// 카테고리 삭제 확인 다이얼로그
  void _showDeleteConfirmDialog(model_category.Category category) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('카테고리 삭제'),
        content: Text('"${category.name}" 카테고리를 삭제하시겠습니까?\n\n카테고리에 상품이 있으면 삭제할 수 없습니다.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('취소'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text('삭제', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      _deleteCategoryFromMemory(category);
    }
  }

  /// 카테고리 추가 (즉시 저장)
  Future<void> _addCategoryToMemory(String name, int? color) async {
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) {
      if (mounted) {
        ToastUtils.showError(context, '현재 행사 워크스페이스가 선택되지 않았습니다.');
      }
      return;
    }

    final newCategory = model_category.Category(
      name: name,
      eventId: currentWorkspace.id,
      sortOrder: _reorderableCategories.length,
      color: color ?? 0xFF2196F3, // 기본 파란색
    );

    try {
      final notifier = ref.read(categoryNotifierProvider.notifier);
      final result = await notifier.addCategory(
        name: name,
        eventId: currentWorkspace.id,
        sortOrder: _reorderableCategories.length,
        color: color ?? 0xFF2196F3,
      );

      if (result['success'] == true) {
        setState(() {
          _reorderableCategories = List.from(_reorderableCategories)..add(newCategory);
        });
      } else {
        if (mounted) {
          final errorMessage = result['errorMessage'] ?? '카테고리 추가에 실패했습니다.';
          ToastUtils.showError(context, errorMessage);
        }
      }
    } catch (e) {
      if (mounted) {
        ToastUtils.showError(context, '카테고리 추가 중 오류가 발생했습니다: $e');
      }
    }
  }

  /// 카테고리 수정 (즉시 저장)
  Future<void> _updateCategoryInMemory(int categoryId, String name, int? color) async {
    final index = _reorderableCategories.indexWhere((cat) => cat.id == categoryId);
    if (index != -1) {
      final oldCategory = _reorderableCategories[index];
      final oldCategoryName = oldCategory.name;

      // 카테고리 이름이 변경된 경우에만 이미지 파일명 업데이트
      if (oldCategoryName != name) {
        await _updateCategoryProductImages(categoryId, oldCategoryName, name);
      }

      try {
        final notifier = ref.read(categoryNotifierProvider.notifier);
        final result = await notifier.updateCategory(
          id: categoryId,
          name: name,
          color: color,
        );

        if (result['success'] == true) {
          setState(() {
            _reorderableCategories = List.from(_reorderableCategories);
            _reorderableCategories[index] = _reorderableCategories[index].copyWith(
              name: name,
              color: color ?? _reorderableCategories[index].color,
            );
          });
        } else {
          if (mounted) {
            final errorMessage = result['errorMessage'] ?? '카테고리 수정에 실패했습니다.';
            ToastUtils.showError(context, errorMessage);
          }
        }
      } catch (e) {
        if (mounted) {
          ToastUtils.showError(context, '카테고리 수정 중 오류가 발생했습니다: $e');
        }
      }
    }
  }

  /// 카테고리 이름 변경 시 해당 카테고리의 모든 상품 이미지 파일명 업데이트
  Future<void> _updateCategoryProductImages(int categoryId, String oldCategoryName, String newCategoryName) async {
    try {
      LoggerUtils.logInfo('카테고리 이미지 파일명 업데이트 시작: $oldCategoryName → $newCategoryName', tag: 'SaleScreen');

      // 현재 워크스페이스/이벤트 ID 가져오기
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace?.id == null) {
        LoggerUtils.logWarning('현재 워크스페이스가 없어 이미지 파일명 업데이트 건너뜀', tag: 'SaleScreen');
        return;
      }

      final eventId = currentWorkspace!.id;

      // 해당 카테고리의 모든 상품 가져오기
      final productState = ref.watch(productNotifierProvider);
      final allProducts = productState.products;
      final categoryProducts = allProducts.where((product) => product.categoryId == categoryId).toList();

      if (categoryProducts.isEmpty) {
        LoggerUtils.logInfo('카테고리에 상품이 없어 이미지 파일명 업데이트 불필요', tag: 'SaleScreen');
        return;
      }

      // 상품별 이미지 정보 준비 (상품명, 현재 이미지 URL)
      final productImageData = categoryProducts
          .where((product) => product.imagePath != null && product.imagePath!.isNotEmpty)
          .map((product) => MapEntry(product.name, product.imagePath))
          .toList();

      if (productImageData.isEmpty) {
        LoggerUtils.logInfo('카테고리에 이미지가 있는 상품이 없어 파일명 업데이트 불필요', tag: 'SaleScreen');
        return;
      }

      // 백그라운드에서 이미지 파일명 업데이트 (DB 사용량 절약 방식)
      final updatedImageUrls = await ImageSyncUtils.renameCategoryProductImagesInStorage(
        eventId,
        oldCategoryName,
        newCategoryName,
        productImageData,
      );

      // 업데이트된 이미지 URL로 상품 정보 업데이트
      final productNotifier = ref.read(productNotifierProvider.notifier);
      for (final product in categoryProducts) {
        final newImageUrl = updatedImageUrls[product.name];
        if (newImageUrl != null && newImageUrl != product.imagePath) {
          await productNotifier.updateProduct(
            product.copyWith(imagePath: newImageUrl),
          );
        }
      }

      LoggerUtils.logInfo('카테고리 이미지 파일명 업데이트 완료: ${updatedImageUrls.length}개 처리', tag: 'SaleScreen');
    } catch (e) {
      LoggerUtils.logError('카테고리 이미지 파일명 업데이트 실패', tag: 'SaleScreen', error: e);
      // 에러가 발생해도 카테고리 이름 변경은 계속 진행
    }
  }

  /// 카테고리 삭제 (즉시 저장)
  Future<void> _deleteCategoryFromMemory(model_category.Category category) async {
    if (category.id == null) return;

    try {
      final notifier = ref.read(categoryNotifierProvider.notifier);
      final success = await notifier.deleteCategory(category.id!);

      if (success) {
        setState(() {
          _reorderableCategories = List.from(_reorderableCategories)
            ..removeWhere((cat) => cat.id == category.id);

          // 순서 재정렬
          for (int i = 0; i < _reorderableCategories.length; i++) {
            _reorderableCategories[i] = _reorderableCategories[i].copyWith(sortOrder: i);
          }
        });
      } else {
        if (mounted) {
          // CategoryRepository에서 설정한 구체적인 에러 메시지 사용
          final categoryState = ref.read(categoryNotifierProvider);
          final errorMessage = categoryState.error?.toString() ?? '카테고리 삭제에 실패했습니다.';
          ToastUtils.showError(context, errorMessage);
        }
      }
    } catch (e) {
      if (mounted) {
        ToastUtils.showError(context, '카테고리 삭제 중 오류가 발생했습니다: $e');
      }
    }
  }

  /// 카테고리 순서 재정렬 (즉시 저장)
  Future<void> _reorderCategories(int oldIndex, int newIndex) async {
    LoggerUtils.logInfo('카테고리 순서 변경 시작: $oldIndex -> $newIndex', tag: 'CategoryManagement');

    if (newIndex > oldIndex) {
      newIndex -= 1;
    }

    // 인덱스 유효성 검사
    if (oldIndex < 0 || oldIndex >= _reorderableCategories.length ||
        newIndex < 0 || newIndex >= _reorderableCategories.length) {
      LoggerUtils.logError('잘못된 인덱스: oldIndex=$oldIndex, newIndex=$newIndex, length=${_reorderableCategories.length}', tag: 'CategoryManagement');
      return;
    }

    final model_category.Category item = _reorderableCategories.removeAt(oldIndex);
    _reorderableCategories.insert(newIndex, item);

    // 순서 재정렬
    final updatedCategories = <model_category.Category>[];
    for (int i = 0; i < _reorderableCategories.length; i++) {
      updatedCategories.add(_reorderableCategories[i].copyWith(sortOrder: i));
    }

    try {
      final notifier = ref.read(categoryNotifierProvider.notifier);

      // ID가 있는 카테고리들만 업데이트
      final categoriesToUpdate = updatedCategories.where((cat) => cat.id != null).toList();
      LoggerUtils.logInfo('업데이트할 카테고리 수: ${categoriesToUpdate.length}', tag: 'CategoryManagement');

      if (categoriesToUpdate.isNotEmpty) {
        // 순서가 변경된 카테고리들을 병렬로 업데이트
        await Future.wait(
          categoriesToUpdate.map((category) =>
            notifier.updateCategory(
              id: category.id!,
              sortOrder: category.sortOrder,
            )
          )
        );

        LoggerUtils.logInfo('카테고리 순서 변경 완료', tag: 'CategoryManagement');
      }

      setState(() {
        _reorderableCategories = updatedCategories;
        _hasChanges = true; // 변경사항 표시
      });
    } catch (e) {
      LoggerUtils.logError('카테고리 순서 변경 실패', tag: 'CategoryManagement', error: e);
      if (mounted) {
        ToastUtils.showError(context, '카테고리 순서 변경 중 오류가 발생했습니다: $e');
        // 실패 시 원래 순서로 복원
        _loadCategories();
      }
    }
  }

  /// 변경사항 저장
  Future<void> _saveChanges() async {
    if (!_hasChanges) {
      Navigator.of(context).pop();
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final notifier = ref.read(categoryNotifierProvider.notifier);
      final currentCategories = ref.read(categoryNotifierProvider).maybeWhen(
        data: (categories) => categories,
        orElse: () => <model_category.Category>[],
      );

      // 변경사항 분석 및 저장
      bool allSuccess = true;

      // 새로 추가된 카테고리들 (id가 null이거나 음수인 것들)
      final newCategories = _reorderableCategories.where((cat) => cat.id == null || (cat.id != null && cat.id! < 0)).toList();
      for (final category in newCategories) {
        final result = await notifier.addCategory(
          name: category.name,
          eventId: category.eventId,
          sortOrder: category.sortOrder,
          color: category.color,
        );
        if (result['success'] != true) allSuccess = false;
      }

      // 기존 카테고리들의 변경사항 처리 (양수 ID를 가진 것들만)
      final existingCategories = _reorderableCategories.where((cat) => cat.id != null && cat.id! > 0).toList();
      for (final category in existingCategories) {
        final original = currentCategories.firstWhere(
          (cat) => cat.id == category.id,
          orElse: () => category,
        );

        // 변경사항이 있는지 확인
        if (original.name != category.name ||
            original.sortOrder != category.sortOrder ||
            original.color != category.color) {
          final result = await notifier.updateCategory(
            id: category.id!,
            name: category.name,
            sortOrder: category.sortOrder,
            color: category.color,
          );
          if (result['success'] != true) allSuccess = false;
        }
      }

      // 삭제된 카테고리들 처리
      final deletedCategories = currentCategories.where((original) =>
        !_reorderableCategories.any((current) => current.id == original.id)
      ).toList();

      for (final category in deletedCategories) {
        final success = await notifier.deleteCategory(category.id!);
        if (!success) allSuccess = false;
      }

      if (mounted) {
        if (allSuccess) {
          Navigator.of(context).pop();
          ToastUtils.showSuccess(context, '카테고리가 저장되었습니다.');
        } else {
          ToastUtils.showError(context, '일부 변경사항 저장에 실패했습니다.');
        }
      }
    } catch (e) {
      if (mounted) {
        ToastUtils.showError(context, '저장 중 오류가 발생했습니다: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final categoriesAsync = ref.watch(categoryNotifierProvider);

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop) {
          await _onBackPressed();
        }
      },
      child: Dialog(
        child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 헤더 (체크리스트 편집 다이얼로그 스타일)
            Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.arrow_back, color: AppColors.onSurfaceVariant),
                  onPressed: _onBackPressed,
                  tooltip: '뒤로가기',
                ),
                const SizedBox(width: 8),
                Text(
                  '카테고리 관리',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: AppColors.onSurface,
                  ),
                ),
                const Spacer(),
                if (_isSaving)
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.primarySeed),
                    ),
                  )
                else
                  IconButton(
                    icon: const Icon(Icons.check, color: AppColors.primarySeed),
                    onPressed: _saveChanges,
                    tooltip: '저장',
                  ),
              ],
            ),
            const SizedBox(height: 16),

            // 본문
            Expanded(
              child: categoriesAsync.when(
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error, size: 64, color: Colors.red),
                  SizedBox(height: 16),
                  Text('카테고리를 불러오는 중 오류가 발생했습니다.'),
                  SizedBox(height: 8),
                  Text('$error', style: TextStyle(color: Colors.grey)),
                ],
              ),
            ),
            data: (categories) {
              if (categories.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.category, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text('등록된 카테고리가 없습니다.'),
                      SizedBox(height: 16),
                      ElevatedButton.icon(
                        onPressed: _showAddCategoryDialog,
                        icon: Icon(Icons.add),
                        label: Text('카테고리 추가'),
                      ),
                    ],
                  ),
                );
              }

              // 순서 변경을 위해 로컬 상태 업데이트 (변경사항이 없을 때만)
              if (_reorderableCategories.length != categories.length && !_hasChanges) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  _loadCategories();
                });
              }

              return Column(
                children: [
                  // 카테고리 목록
                  Expanded(
                    child: ReorderableListView.builder(
                      itemCount: _reorderableCategories.length,
                      onReorder: _reorderCategories,
                      buildDefaultDragHandles: false,
                      itemBuilder: (context, index) {
                        final category = _reorderableCategories[index];
                        return Card(
                          key: ValueKey(category.id ?? 'category_$index'),
                          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                          child: _buildCategoryListItem(category, index),
                        );
                      },
                    ),
                  ),
                ],
              );
            },
          ),
        ),

        // 하단 추가 버튼
        const SizedBox(height: 16),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _showAddCategoryDialog,
            icon: const Icon(Icons.add),
            label: const Text('새 카테고리 추가'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    ),
        ),
      ),
    );
  }
}

/// 카테고리 추가 다이얼로그
class _AddCategoryDialog extends StatefulWidget {
  const _AddCategoryDialog();

  @override
  State<_AddCategoryDialog> createState() => _AddCategoryDialogState();
}

class _AddCategoryDialogState extends State<_AddCategoryDialog> {
  final _nameController = TextEditingController();
  int _selectedColor = 0xFF2196F3; // 기본 파란색

  // 기본 색상 목록
  final _categoryColors = [
    0xFF2196F3, // Blue
    0xFF4CAF50, // Green
    0xFFFF9800, // Orange
    0xFFE91E63, // Pink
    0xFF9C27B0, // Purple
    0xFF00BCD4, // Cyan
    0xFFFF5722, // Deep Orange
    0xFF795548, // Brown
    0xFF607D8B, // Blue Grey
    0xFFF44336, // Red
  ];

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('카테고리 추가'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 카테고리 이름 입력
            TextField(
              controller: _nameController,
              decoration: InputDecoration(
                labelText: '카테고리 이름',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.folder),
              ),
              autofocus: true,
            ),
            SizedBox(height: 16),

            // 색상 선택
            Text(
              '카테고리 색상',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _categoryColors.map((color) {
                final isSelected = _selectedColor == color;
                return GestureDetector(
                  onTap: () => setState(() => _selectedColor = color),
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Color(color),
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: isSelected ? Colors.black : Colors.transparent,
                        width: 3,
                      ),
                    ),
                    child: isSelected
                        ? Icon(Icons.check, color: Colors.white, size: 20)
                        : null,
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('취소'),
        ),
        ElevatedButton(
          onPressed: () {
            final name = _nameController.text.trim();
            if (name.isNotEmpty) {
              Navigator.pop(context, {
                'name': name,
                'color': _selectedColor,
              });
            }
          },
          child: Text('추가'),
        ),
      ],
    );
  }
}

/// 카테고리 수정 다이얼로그
class _EditCategoryDialog extends StatefulWidget {
  final model_category.Category category;

  const _EditCategoryDialog({required this.category});

  @override
  State<_EditCategoryDialog> createState() => _EditCategoryDialogState();
}

class _EditCategoryDialogState extends State<_EditCategoryDialog> {
  late final TextEditingController _nameController;
  late int _selectedColor;

  // 기본 색상 목록
  final _categoryColors = [
    0xFF2196F3, // Blue
    0xFF4CAF50, // Green
    0xFFFF9800, // Orange
    0xFFE91E63, // Pink
    0xFF9C27B0, // Purple
    0xFF00BCD4, // Cyan
    0xFFFF5722, // Deep Orange
    0xFF795548, // Brown
    0xFF607D8B, // Blue Grey
    0xFFF44336, // Red
  ];

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.category.name);
    _selectedColor = widget.category.color;
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('카테고리 수정'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 카테고리 이름 입력
            TextField(
              controller: _nameController,
              decoration: InputDecoration(
                labelText: '카테고리 이름',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.folder),
              ),
              autofocus: true,
            ),
            SizedBox(height: 16),

            // 색상 선택
            Text(
              '카테고리 색상',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _categoryColors.map((color) {
                final isSelected = _selectedColor == color;
                return GestureDetector(
                  onTap: () => setState(() => _selectedColor = color),
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Color(color),
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: isSelected ? Colors.black : Colors.transparent,
                        width: 3,
                      ),
                    ),
                    child: isSelected
                        ? Icon(Icons.check, color: Colors.white, size: 20)
                        : null,
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('취소'),
        ),
        ElevatedButton(
          onPressed: () {
            final name = _nameController.text.trim();
            if (name.isNotEmpty) {
              Navigator.pop(context, {
                'name': name,
                'color': _selectedColor,
              });
            }
          },
          child: Text('수정'),
        ),
      ],
    );
  }
}

/// UI 열 수 설정 다이얼로그
class _UIColumnsSettingsDialog extends StatefulWidget {
  final int portraitColumns;
  final int landscapeColumns;
  final ValueChanged<int> onPortraitColumnsChanged;
  final ValueChanged<int> onLandscapeColumnsChanged;

  const _UIColumnsSettingsDialog({
    required this.portraitColumns,
    required this.landscapeColumns,
    required this.onPortraitColumnsChanged,
    required this.onLandscapeColumnsChanged,
  });

  @override
  State<_UIColumnsSettingsDialog> createState() => _UIColumnsSettingsDialogState();
}

class _UIColumnsSettingsDialogState extends State<_UIColumnsSettingsDialog> {
  late int _portraitColumns;
  late int _landscapeColumns;

  @override
  void initState() {
    super.initState();
    // 세로모드: 2-5열 범위 (스마트폰), 4-7열 범위 (타블렛)
    _portraitColumns = widget.portraitColumns.clamp(2, 7);
    // 가로모드: 5-10열 범위 (스마트폰), 7-12열 범위 (타블렛)
    _landscapeColumns = widget.landscapeColumns.clamp(5, 12);
  }

  @override
  Widget build(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        final isTablet = ref.watch(isTabletDeviceProvider);

    return custom_dialog.DialogTheme.buildResponsiveDialog(
      isCompact: true,
      child: Padding(
        padding: custom_dialog.DialogTheme.getCompactDialogPadding(isTablet),
        child: SingleChildScrollView( // 스크롤 가능하도록 추가
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 제목
              Center(
                child: Text(
                  'UI 열 수 설정',
                  style: custom_dialog.DialogTheme.titleStyle.copyWith(
                    fontSize: isTablet ? 20.0 : 18.0,
                    fontFamily: 'Pretendard',
                  ),
                ),
              ),
              SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),
              // 세로모드 설정
              _buildCompactSlider(
                '세로모드',
                Icons.stay_current_portrait_rounded,
                _portraitColumns.toDouble(),
                (value) => setState(() => _portraitColumns = value.round()),
                isTablet,
                min: isTablet ? 4.0 : 3.0, // 스마트폰에서 최소값을 3으로 변경하여 오류 방지
                max: isTablet ? 7.0 : 5.0,
              ),
              const SizedBox(height: 16),

              // 가로모드 설정
              _buildCompactSlider(
                '가로모드',
                Icons.stay_current_landscape_rounded,
                _landscapeColumns.toDouble(),
                (value) => setState(() => _landscapeColumns = value.round()),
                isTablet,
                min: isTablet ? 7.0 : 5.0,
                max: isTablet ? 12.0 : 10.0,
              ),
              SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),

              // 버튼 (각각 절반씩 사용)
              Row(
                children: [
                  Expanded(
                    child: custom_dialog.DialogTheme.buildModernButton(
                      text: '취소',
                      onPressed: () => Navigator.of(context).pop(),
                      isTablet: isTablet,
                      isPrimary: false,
                    ),
                  ),
                  SizedBox(width: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),
                  Expanded(
                    child: custom_dialog.DialogTheme.buildModernButton(
                      text: '확인',
                      onPressed: () {
                        widget.onPortraitColumnsChanged(_portraitColumns);
                        widget.onLandscapeColumnsChanged(_landscapeColumns);
                        Navigator.of(context).pop();
                      },
                      isTablet: isTablet,
                      isPrimary: true,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
      },
    );
  }

  /// 컴팩트한 슬라이더 빌더
  Widget _buildCompactSlider(
    String label,
    IconData icon,
    double value,
    ValueChanged<double> onChanged,
    bool isTablet, {
    double min = 3.0,
    double max = 12.0,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 라벨과 현재 값
        Row(
          children: [
            Icon(
              icon,
              color: Theme.of(context).colorScheme.primary,
              size: 18,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                label,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Pretendard',
                ),
              ),
            ),
            // 현재 값 표시
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                '${value.round()}열',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onPrimary,
                  fontFamily: 'Pretendard',
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        // 슬라이더
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: Theme.of(context).colorScheme.primary,
            inactiveTrackColor: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
            thumbColor: Theme.of(context).colorScheme.primary,
            overlayColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.15),
            trackHeight: 4.0,
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 12.0),
            overlayShape: const RoundSliderOverlayShape(overlayRadius: 20.0),
            tickMarkShape: const RoundSliderTickMarkShape(tickMarkRadius: 2.0),
            activeTickMarkColor: Theme.of(context).colorScheme.primary,
            inactiveTickMarkColor: Theme.of(context).colorScheme.outline.withValues(alpha: 0.5),
          ),
          child: Slider(
            value: value,
            min: min,
            max: max,
            divisions: (max - min).round(),
            onChanged: onChanged,
          ),
        ),
        const SizedBox(height: 8),
        // 숫자 라벨
        _buildSliderLabels(min, max, value),
      ],
    );
  }

  /// 슬라이더 숫자 라벨 빌더
  Widget _buildSliderLabels(double min, double max, double currentValue) {
    final labels = <Widget>[];
    final range = max - min;
    final steps = range.round();

    for (int i = 0; i <= steps; i++) {
      final value = min + i;
      final isSelected = value == currentValue;

      labels.add(
        Expanded(
          child: Center(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: isSelected ? BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
                borderRadius: BorderRadius.circular(12),
              ) : null,
              child: Text(
                value.round().toString(),
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                  color: isSelected
                    ? Theme.of(context).colorScheme.onPrimary
                    : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  fontFamily: 'Pretendard',
                ),
              ),
            ),
          ),
        ),
      );
    }

    return Row(children: labels);
  }
}
