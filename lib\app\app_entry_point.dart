import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:syncfusion_localizations/syncfusion_localizations.dart';
import '../screens/splash/splash_screen.dart';
import '../utils/logger_utils.dart';
import '../screens/onboarding/onboarding_screen.dart';
import '../screens/auth/login_screen.dart';
import '../screens/auth/register_screen.dart';
// 로컬 전용 모드: 동기화 화면 제거됨
import 'app_wrapper.dart';
import '../screens/inventory/inventory_screen.dart';
import '../screens/settings/my_page_screen.dart';
import '../screens/product/register_product_screen.dart';
import '../screens/prepayment/register_prepayment_screen.dart';
import '../screens/sale/sale_screen.dart';
import '../screens/checklist/checklist_screen.dart';
import '../screens/auth/nickname_screen.dart';
import '../providers/nickname_provider.dart';
import '../screens/sales_log/sales_log_screen.dart';
import '../screens/statistics/statistics_screen.dart';
import '../screens/records_and_statistics/records_and_statistics_screen.dart';
import '../screens/settings/settings_screen.dart';
import '../pages/admin/admin_login_page.dart';
import '../pages/admin/admin_dashboard_page.dart';

import '../providers/unified_workspace_provider.dart';
// 로컬 전용 모드: 실시간 동기화 제거됨
import '../utils/safe_dialog_utils.dart';

import '../services/subscription_service.dart';
import '../models/subscription_plan.dart';
import 'app_themes.dart';

/// 앱 진입점: Splash(Firebase 초기화) → 온보딩/로그인 플로우
class AppEntryPoint extends StatefulWidget {
  const AppEntryPoint({super.key});

  @override
  State<AppEntryPoint> createState() => _AppEntryPointState();
}

class _AppEntryPointState extends State<AppEntryPoint> {
  bool _isOnboarded = false;
  bool _isLoading = true;
  String _authMode = 'login'; // 'login' or 'register'
  bool _needsSyncCheck = false; // 동기화 확인이 필요한지 여부
  DateTime? _splashStartTime; // 스플래시 화면 시작 시간
  bool _skipAccountValidation = false; // 로그인 성공 직후 검증 건너뛰기

  // 🔥 미리 준비된 홈 위젯 (OnboardingScreen 깜빡임 방지)
  Widget? _prebuiltHomeWidget;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  void initState() {
    super.initState();
    _splashStartTime = DateTime.now(); // 스플래시 시작 시간 기록
    // 🔥 온보딩 체크는 스플래시 완료 후에만 수행 (중복 제거)
  }

  /// 백그라운드에서 온보딩 상태 체크 (UI 변경 없이 결과만 저장)
  Future<void> _checkOnboardingInBackground() async {
    try {
      await _checkOnboarding();
    } catch (e) {
      // 오류 발생 시 기본값 설정
      _isOnboarded = false;
      _authMode = 'login';
    }
  }



  /// 스플래시 화면 완료 후 호출되는 콜백
  void _onSplashComplete() async {
    // 🔥 온보딩 상태 확인이 완료될 때까지 대기
    await _checkOnboardingInBackground();

    // 🔥 홈 위젯을 미리 준비
    final homeWidget = await _buildHome();

    _finishSplashScreen(
      isOnboarded: _isOnboarded,
      authMode: _authMode,
      needsSyncCheck: _needsSyncCheck,
      prebuiltHome: homeWidget,
    );
  }

  /// 스플래시 화면 최소 시간 보장 후 로딩 완료 처리
  Future<void> _finishSplashScreen({
    bool? isOnboarded,
    String? authMode,
    bool? needsSyncCheck,
    Widget? prebuiltHome,
  }) async {
    const minSplashDuration = Duration(seconds: 2); // 최소 2초 스플래시 표시

    if (_splashStartTime != null) {
      final elapsed = DateTime.now().difference(_splashStartTime!);
      final remainingTime = minSplashDuration - elapsed;

      if (remainingTime > Duration.zero) {
        // 최소 시간이 지나지 않았으면 추가 대기
        await Future.delayed(remainingTime);
      }
    }

    if (mounted) {
      setState(() {
        if (isOnboarded != null) _isOnboarded = isOnboarded;
        if (authMode != null) _authMode = authMode;
        if (needsSyncCheck != null) _needsSyncCheck = needsSyncCheck;
        if (prebuiltHome != null) _prebuiltHomeWidget = prebuiltHome;
        _isLoading = false;
      });
    }
  }

  Future<void> _checkOnboarding() async {
    final prefs = await SharedPreferences.getInstance();

    // 이메일 인증 완료 플래그 확인 (회원가입 플로우에서 온보딩 건너뛰기)
    final emailVerificationCompleted = prefs.getBool('email_verification_completed') ?? false;
    if (emailVerificationCompleted) {
      await prefs.remove('email_verification_completed');
      LoggerUtils.logInfo('🔄 이메일 인증 완료 - 온보딩 건너뛰고 로그인 화면으로', tag: 'AppEntryPoint');

      // 온보딩은 완료된 상태로 설정하여 로그인 화면으로 바로 이동
      _isOnboarded = true;
      _authMode = 'login';
      return;
    }

    // 로그아웃 완료 플래그 확인
    final logoutCompleted = prefs.getBool('logout_completed') ?? false;
    if (logoutCompleted) {
      await prefs.remove('logout_completed');
      LoggerUtils.logInfo('🔄 로그아웃 완료 - 로그인 화면으로 이동', tag: 'AppEntryPoint');

      // Firebase 인증 상태도 확실히 클리어
      try {
        await FirebaseAuth.instance.signOut();
      } catch (e) {
        LoggerUtils.logError('Firebase 로그아웃 실패 - 계속 진행', tag: 'AppEntryPoint', error: e);
      }

      // 로그아웃 상태 저장 (UI 변경 없음)
      _isOnboarded = true; // 온보딩은 완료된 상태로 유지
      _authMode = 'login';

      LoggerUtils.logInfo('로그아웃 플래그 처리 완료 - 로그인 페이지로 이동', tag: 'AppEntryPoint');
      return;
    }
    
    // 앱 재시작 플래그 확인 (회원탈퇴 후)
    final forceAppRestart = prefs.getBool('force_app_restart') ?? false;
    if (forceAppRestart) {
      await prefs.remove('force_app_restart');
      LoggerUtils.logInfo('🔄 회원탈퇴 후 앱 재시작 - 온보딩 상태 초기화', tag: 'AppEntryPoint');
      // 온보딩 상태 저장 (UI 변경 없음)
      _isOnboarded = false;
      return;
    }
    
    // 계정 삭제 플래그 확인 및 알림 표시
    final accountDeletedByOtherDevice = prefs.getBool('account_deleted_by_other_device') ?? false;
    if (accountDeletedByOtherDevice) {
      await prefs.remove('account_deleted_by_other_device');
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showAccountDeletedDialog();
      });
    }
    
    // 디버그 모드에서만 추가 검증 로직
    bool forceReset = false;
    bool debugModeReset = false;
    
    assert(() {
      // 디버그 모드에서만 실행되는 코드
      LoggerUtils.logInfo('🔍 디버그 모드: 온보딩 상태 상세 분석 시작', tag: 'AppEntryPoint');
      
      // 1. 기존 강제 리셋 플래그 확인
      forceReset = prefs.getBool('force_onboarding_reset') ?? false;
      if (forceReset) {
        LoggerUtils.logInfo('🔄 강제 온보딩 초기화 플래그 발견', tag: 'AppEntryPoint');
      }
      
      // 2. SharedPreferences 상태 상세 로깅
      final currentOnboardedState = prefs.getBool('isOnboarded');
      final allKeys = prefs.getKeys();
      LoggerUtils.logInfo('📋 SharedPreferences 상태:', tag: 'AppEntryPoint');
      LoggerUtils.logInfo('  - isOnboarded: $currentOnboardedState', tag: 'AppEntryPoint');
      LoggerUtils.logInfo('  - force_onboarding_reset: ${prefs.getBool('force_onboarding_reset')}', tag: 'AppEntryPoint');
      LoggerUtils.logInfo('  - 전체 키 개수: ${allKeys.length}', tag: 'AppEntryPoint');
      
      // 3. Firebase 사용자 상태 확인
      final user = FirebaseAuth.instance.currentUser;
      LoggerUtils.logInfo('🔐 Firebase 사용자 상태: ${user?.email ?? 'null'}', tag: 'AppEntryPoint');
      
      // 4. 디버그 모드에서의 추가 검증 로직 (이메일 인증 대기 상태 고려)
      if (currentOnboardedState == true && user == null) {
        // 이메일 인증 대기 상태인지 확인
        final isEmailVerificationPending = prefs.getBool('email_verification_pending') ?? false;
        
        if (isEmailVerificationPending) {
          LoggerUtils.logInfo('📧 이메일 인증 대기 중 - 온보딩 리셋하지 않음', tag: 'AppEntryPoint');
        } else {
          LoggerUtils.logInfo('⚠️  의심스러운 상태 감지: 온보딩 완료되었지만 Firebase 사용자 없음', tag: 'AppEntryPoint');
          LoggerUtils.logInfo('🔄 디버그 모드: 온보딩 상태 자동 리셋', tag: 'AppEntryPoint');
          debugModeReset = true;
        }
      }
      
      return true;
    }());

    // 리셋 실행
    if (forceReset || debugModeReset) {
      LoggerUtils.logInfo('🔄 온보딩 상태 리셋 실행', tag: 'AppEntryPoint');
      await prefs.remove('isOnboarded');
      await prefs.remove('force_onboarding_reset');
    }

    final isOnboarded = (forceReset || debugModeReset) ? false : (prefs.getBool('isOnboarded') ?? false);
    final user = FirebaseAuth.instance.currentUser;
    
    // 최종 상태 로깅
    LoggerUtils.logInfo('=== 온보딩 상태 확인 결과 ===', tag: 'AppEntryPoint');
    LoggerUtils.logInfo('isOnboarded: $isOnboarded', tag: 'AppEntryPoint');
    LoggerUtils.logInfo('Firebase User: ${user?.email ?? 'null'}', tag: 'AppEntryPoint');
    LoggerUtils.logInfo('Platform: ${Platform.isIOS ? 'iOS' : 'Android'}', tag: 'AppEntryPoint');
    LoggerUtils.logInfo('Force Reset: $forceReset', tag: 'AppEntryPoint');
    LoggerUtils.logInfo('Debug Mode Reset: $debugModeReset', tag: 'AppEntryPoint');
    LoggerUtils.logInfo('Final Decision: ${!isOnboarded ? "온보딩 화면 표시" : "로그인/메인 화면 표시"}', tag: 'AppEntryPoint');
    
    // 온보딩 상태 저장 (UI 변경 없음)
    _isOnboarded = isOnboarded;
  }

  void _completeOnboarding() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isOnboarded', true);

    // 상태를 먼저 업데이트
    _isOnboarded = true;

    // 온보딩 완료 후 새로운 홈 위젯 빌드
    final newHomeWidget = await _buildHome();

    setState(() {
      _prebuiltHomeWidget = newHomeWidget; // 🔥 새로운 홈 위젯으로 업데이트
    });
  }

  Future<bool> _validateUserAccount() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return true; // 로그인되지 않은 상태는 정상

    try {
      LoggerUtils.logInfo('사용자 계정 유효성 검증 시작: ${user.email}', tag: 'AppEntryPoint');

      // 로컬 전용 모드: 계정 검증 항상 성공
      LoggerUtils.logInfo('로컬 전용 모드: 사용자 계정 유효성 검증 완료', tag: 'AppEntryPoint');
      return true;

    } catch (e) {
      LoggerUtils.logError('사용자 계정 유효성 검증 실패', tag: 'AppEntryPoint', error: e);
      // 검증 실패 시에는 일시적 문제로 간주하여 계속 진행
      return true;
    } finally {
      // 계정 검증 완료
    }
  }





  void _switchAuthMode() async {
    _authMode = _authMode == 'login' ? 'register' : 'login';

    // 인증 모드 변경 후 새로운 홈 위젯 빌드
    try {
      final newHomeWidget = await _buildHome();
      if (mounted) {
        setState(() {
          _prebuiltHomeWidget = newHomeWidget;
        });
      }
    } catch (e) {
      LoggerUtils.logError('인증 모드 변경 후 홈 위젯 빌드 실패', tag: 'AppEntryPoint', error: e);
      if (mounted) {
        setState(() {
          _prebuiltHomeWidget = null;
        });
      }
    }
  }

  /// 계정 삭제 알림 다이얼로그 표시
  void _showAccountDeletedDialog() {
    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('계정이 삭제되었습니다'),
        content: const Text(
          '다른 기기에서 회원탈퇴가 진행되어 계정이 삭제되었습니다.\n'
          '로컬 데이터가 모두 삭제되었으며, 로그인 화면으로 이동합니다.'
        ),
        actions: [
          ElevatedButton(
            onPressed: () async {
              SafeDialogUtils.safePopDialog(context);
              // 온보딩 상태를 true로 유지하고 새로운 홈 위젯 빌드
              _isOnboarded = true; // 온보딩은 완료된 상태로 유지

              try {
                final newHomeWidget = await _buildHome();
                if (mounted) {
                  setState(() {
                    _prebuiltHomeWidget = newHomeWidget;
                  });
                }
              } catch (e) {
                LoggerUtils.logError('계정 삭제 후 홈 위젯 빌드 실패', tag: 'AppEntryPoint', error: e);
                if (mounted) {
                  setState(() {
                    _prebuiltHomeWidget = null;
                  });
                }
              }
            },
            child: const Text('확인'),
          ),
        ],
      ),
    );
  }

  void _onLoginSuccess({required bool hasServerData}) async {
    try {
      LoggerUtils.logInfo('🔥 _onLoginSuccess 호출됨 - hasServerData: $hasServerData', tag: 'AppEntryPoint');

      final container = ProviderScope.containerOf(context, listen: false);

      // SharedPreferences 정리 (일회성 플래그 제거)
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('has_server_data_on_login');

      LoggerUtils.logInfo('로그인 성공 처리 시작 - 서버 데이터 존재 여부: $hasServerData', tag: 'AppEntryPoint');

      // 🔥 플랜별 동기화 제한 추가 확인 (이중 안전장치)
      bool finalNeedsSyncCheck = hasServerData;
      try {
        final subscriptionService = SubscriptionService();
        final planType = await subscriptionService.getCurrentPlanType();

        if (planType == SubscriptionPlanType.free || planType == SubscriptionPlanType.plus) {
          LoggerUtils.logInfo('프리/플러스 플랜 감지 - 동기화 페이지 강제 스킵: $planType', tag: 'AppEntryPoint');
          finalNeedsSyncCheck = false;
        }
      } catch (e) {
        LoggerUtils.logError('플랜 확인 실패 - 기본값 사용', tag: 'AppEntryPoint', error: e);
      }

      LoggerUtils.logInfo('_needsSyncCheck 최종 설정: $finalNeedsSyncCheck (원본: $hasServerData)', tag: 'AppEntryPoint');

      // 라우팅 관련 플래그를 즉시 설정
      _skipAccountValidation = true;   // 로그인 직후 검증 건너뛰기
      _needsSyncCheck = finalNeedsSyncCheck; // 동기화 페이지 여부 즉시 결정

      // 로그인 성공 후 바로 다음 화면으로 이동 (스플래시 건너뛰기)

      if (!mounted) {
        return;
      }

      // 닉네임 상태 확인
      final nickname = container.read(nicknameProvider);
      final hasNickname = nickname != null && nickname.name.isNotEmpty;

      Widget nextWidget;
      if (hasNickname) {
        LoggerUtils.logInfo('닉네임 있음 - AppWrapper로 이동', tag: 'AppEntryPoint');
        nextWidget = const AppWrapper(child: InventoryScreen());
      } else {
        LoggerUtils.logInfo('닉네임 없음 - NicknameScreen으로 이동', tag: 'AppEntryPoint');
        nextWidget = NicknameScreen(
          onNicknameSet: () {
            // 닉네임 설정 완료 후 AppWrapper로 이동
            if (mounted) {
              setState(() {
                _prebuiltHomeWidget = const AppWrapper(child: InventoryScreen());
              });
            }
          },
        );
      }

      LoggerUtils.logInfo('setState로 바로 다음 화면 설정 (스플래시 건너뛰기)', tag: 'AppEntryPoint');
      setState(() {
        _prebuiltHomeWidget = nextWidget; // 🔥 바로 다음 화면으로 설정
        _isLoading = false; // 🔥 로그인 성공 시 스플래시 건너뛰기
      });
      LoggerUtils.logInfo('다음 화면 설정 완료 (스플래시 건너뛰기)', tag: 'AppEntryPoint');
      
      // 3) 부수 작업은 백그라운드에서 수행 (라우팅에 영향 없음)
      if (hasServerData) {
        // 워크스페이스 정보만 선로드 (실패해도 라우팅에는 영향 없음)
        Future(() async {
          try {
            await container.read(unifiedWorkspaceProvider.notifier).refresh();
            LoggerUtils.logInfo('워크스페이스 정보 로드 완료', tag: 'AppEntryPoint');
          } catch (e) {
            LoggerUtils.logError('워크스페이스 정보 로드 실패', tag: 'AppEntryPoint', error: e);
          }
        });
      }
      
      // 로컬 전용 모드: 실시간 동기화 서비스 제거됨
    } catch (e) {
      LoggerUtils.logError('서버 데이터 확인 실패', tag: 'AppEntryPoint', error: e);

      // 오류 발생 시에도 새로운 홈 위젯 빌드
      _skipAccountValidation = true; // 그래도 로그인 직후 플로우는 진행
      _needsSyncCheck = false;

      try {
        final newHomeWidget = await _buildHome();
        if (!mounted) return;
        setState(() {
          _prebuiltHomeWidget = newHomeWidget;
          _isLoading = false; // 🔥 오류 발생 시에도 스플래시 건너뛰기
        });
      } catch (buildError) {
        LoggerUtils.logError('홈 위젯 빌드 실패', tag: 'AppEntryPoint', error: buildError);
        if (!mounted) return;
        setState(() {
          _prebuiltHomeWidget = null;
          _isLoading = false; // 🔥 빌드 실패 시에도 스플래시 건너뛰기
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return MaterialApp(
        debugShowCheckedModeBanner: false,
        home: SplashScreen(
          onInitializationComplete: _onSplashComplete,
        ),
      );
    }
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: '바라 부스 매니저',
      theme: _buildLightTheme(),
      darkTheme: _buildLightTheme(), // 다크모드 비활성화 - 라이트 테마로 고정
      themeMode: ThemeMode.light, // 다크모드 완전 비활성화
      locale: const Locale('ko', 'KR'),
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        SfGlobalLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('ko', 'KR'),
        Locale('en', 'US'),
      ],
      routes: {
        '/my_page': (context) => const MyPageScreen(),
        '/inventory': (context) => const AppWrapper(child: InventoryScreen()),
        '/register_product': (context) => const AppWrapper(child: RegisterProductScreen()),
        '/register_prepayment': (context) => const AppWrapper(child: RegisterPrepaymentScreen()),
        '/sale': (context) => const AppWrapper(child: SaleScreen()),
        '/checklist': (context) => const ChecklistScreen(),
        '/sales_log': (context) => const AppWrapper(child: SalesLogScreen()),
        '/statistics': (context) => const AppWrapper(child: StatisticsScreen()),
        '/records_and_statistics': (context) => const AppWrapper(child: RecordsAndStatisticsScreen()),
        '/settings': (context) => const AppWrapper(child: SettingsScreen()),
        '/para@admin': (context) => const AdminLoginPage(),
        '/para@admin/dashboard': (context) => const AdminDashboardPage(),
      },
      home: _prebuiltHomeWidget ?? OnboardingScreen(onStart: _completeOnboarding),
    );
  }

  Future<Widget> _buildHome() async {
    try {
      final user = FirebaseAuth.instance.currentUser;

      LoggerUtils.logInfo('_isOnboarded: $_isOnboarded', tag: 'AppEntryPoint');
      LoggerUtils.logInfo('user: ${user?.email ?? 'null'}', tag: 'AppEntryPoint');
      LoggerUtils.logInfo('_needsSyncCheck: $_needsSyncCheck', tag: 'AppEntryPoint');
      LoggerUtils.logInfo('_skipAccountValidation: $_skipAccountValidation', tag: 'AppEntryPoint');

      if (!_isOnboarded) {
        LoggerUtils.logInfo('→ OnboardingScreen으로 이동', tag: 'AppEntryPoint');
        return OnboardingScreen(onStart: _completeOnboarding);
      } else if (user == null) {
        LoggerUtils.logInfo('→ Auth Screen으로 이동 (mode: $_authMode)', tag: 'AppEntryPoint');
        if (_authMode == 'login') {
          return LoginScreen(
            onLoginSuccess: (hasServerData) => _onLoginSuccess(hasServerData: hasServerData),
            onRegisterRequested: _switchAuthMode,
          );
        } else {
          return RegisterScreen(
            onRegisterSuccess: _switchAuthMode,
            onLoginRequested: _switchAuthMode,
          );
        }
      } else {
        // 로그인된 사용자가 있는 경우
        LoggerUtils.logInfo('로그인된 사용자 감지 - 플로우 시작: ${user.email}', tag: 'AppEntryPoint');

        // 로그인 성공 직후이거나 이미 검증 완료된 경우 검증 건너뛰기
        if (_skipAccountValidation) {
          
          // 검증 건너뛰기 플래그 초기화 (다음 앱 시작 시를 위해)
          _skipAccountValidation = false;
          
          // 로컬 전용 모드: 동기화 확인 화면 제거됨, 바로 홈으로 이동
          _needsSyncCheck = false;

          LoggerUtils.logInfo('→ AppWrapper로 이동', tag: 'AppEntryPoint');
          return const AppWrapper(child: InventoryScreen());
        }
        
        // 앱 시작 시 계정 유효성 검증 (동기적으로 수행하여 확실한 검증)
        final isAccountValid = await _validateUserAccount();
        if (!isAccountValid && mounted) {
          // 계정이 유효하지 않으면 로그인 화면으로 이동
          setState(() {
            _isOnboarded = false;
            _authMode = 'login';
          });
          return OnboardingScreen(onStart: _completeOnboarding);
        }

        // 로컬 전용 모드: 동기화 확인 화면 제거됨, 바로 메인 화면으로 이동
        _needsSyncCheck = false;
        LoggerUtils.logInfo('→ AppWrapper로 이동', tag: 'AppEntryPoint');
        return const AppWrapper(child: InventoryScreen());
      }
    } catch (e) {
      // Firebase 관련 오류 시 온보딩 화면으로 이동
      LoggerUtils.logError('Firebase 인증 확인 실패', tag: 'AppEntryPoint', error: e);
      if (!_isOnboarded) {
        return OnboardingScreen(onStart: _completeOnboarding);
      } else {
        return const AppWrapper(child: InventoryScreen());
      }
    }
  }
}

ThemeData _buildLightTheme() {
  return AppThemes.light();
}
